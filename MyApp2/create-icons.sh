#!/bin/bash

# Create the iconset directory
mkdir -p assets/icon.iconset

# Convert SVG to PNG in different sizes
for size in 16 32 64 128 256 512 1024; do
  # Regular size
  convert -background none -size ${size}x${size} assets/icon.svg assets/icon.iconset/icon_${size}x${size}.png
  
  # Double size (for Retina displays)
  if [ $size -lt 512 ]; then
    double=$((size * 2))
    convert -background none -size ${double}x${double} assets/icon.svg assets/icon.iconset/icon_${size}x${size}@2x.png
  fi
done

# Create the icns file
iconutil -c icns assets/icon.iconset -o assets/icon.icns

echo "Icon files created successfully!"
