#!/bin/bash

# Build the Electron main process
echo "Building Electron main process..."
npm run build:electron

# Build the renderer process
echo "Building renderer process..."
npm run build:renderer

# Ensure the dist/electron directory exists
if [ ! -d "dist/electron" ]; then
  echo "Error: dist/electron directory not found after build!"
  exit 1
fi

echo "Build completed successfully!"
echo "You can now run 'npm start' to start the application."
