{"version": 3, "file": "TranscriptionHelper.js", "sourceRoot": "", "sources": ["../../src/electron/TranscriptionHelper.ts"], "names": [], "mappings": ";;;;;;AAAA,2CAAwC;AACxC,+BAAqC;AACrC,uCAAiD;AACjD,oEAAmC;AACnC,4DAA+B;AAE/B,iDAAyC;AAgBzC,MAAa,mBAAmB;IAY9B;QATQ,cAAS,GAAwB,IAAI,CAAC;QACtC,eAAU,GAAW,EAAE,CAAC;QACxB,qBAAgB,GAAa,EAAE,CAAC,CAAC,6DAA6D;QAC9F,mBAAc,GAAY,KAAK,CAAC;QAChC,gBAAW,GAAG,KAAK,CAAC;QACpB,eAAU,GAAG,GAAG,CAAC,CAAC,wDAAwD;QAC1E,0BAAqB,GAAG,KAAK,CAAC,CAAC,yDAAyD;QACxF,oBAAe,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAuOrC,qDAAqD;QAC7C,eAAU,GAAyB,IAAI,CAAC;QArO9C,kDAAkD;QAClD,MAAM,KAAK,GAAG,IAAI,wBAAK,EAAE,CAAC;QAC1B,MAAM,OAAO,GAAG,KAAK,CAAC,GAAG,CAAC,SAAS,CAA2B,IAAI,EAAE,UAAU,EAAE,EAAE,EAAE,CAAC;QACrF,MAAM,MAAM,GAAG,OAAO,CAAC,UAAU,IAAI,OAAO,CAAC,GAAG,CAAC,gBAAgB,IAAI,EAAE,CAAC;QAExE,+BAA+B;QAC/B,IAAI,CAAC,MAAM,GAAG,IAAI,uBAAU,CAAC;YAC3B,MAAM,EAAE,MAAM;SACf,CAAC,CAAC;IACL,CAAC;IAED;;;OAGG;IACK,iBAAiB;QACvB,IAAI,CAAC;YACH,+BAA+B;YAC/B,IAAA,wBAAQ,EAAC,eAAe,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE,CAAC,CAAC;YAC/C,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,sCAAsC,EAAE,KAAK,CAAC,CAAC;YAC7D,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;OAEG;IACK,yBAAyB;QAC/B,iBAAM,CAAC,cAAc,CAAC;YACpB,IAAI,EAAE,SAAS;YACf,KAAK,EAAE,cAAc;YACrB,OAAO,EAAE,6DAA6D;YACtE,MAAM,EAAE,uDAAuD;gBACvD,2BAA2B;gBAC3B,wDAAwD;gBACxD,0CAA0C;YAClD,OAAO,EAAE,CAAC,IAAI,CAAC;YACf,SAAS,EAAE,CAAC;SACb,CAAC,CAAC;IACL,CAAC;IAEM,YAAY,CAAC,MAAc;QAChC,yCAAyC;QACzC,IAAI,CAAC,MAAM,GAAG,IAAI,uBAAU,CAAC;YAC3B,MAAM,EAAE,MAAM;SACf,CAAC,CAAC;IACL,CAAC;IAEM,KAAK,CAAC,cAAc,CAAC,MAAe;QACzC,IAAI,CAAC;YACH,8CAA8C;YAC9C,MAAM,OAAO,GAAG,MAAM,IAAK,IAAI,CAAC,MAAc,CAAC,MAAM,CAAC;YAEtD,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,OAAO,KAAK,CAAC;YACf,CAAC;YAED,8EAA8E;YAC9E,MAAM,UAAU,GAAG,MAAM;gBACvB,CAAC,CAAC,IAAI,uBAAU,CAAC,EAAE,MAAM,EAAE,CAAC;gBAC5B,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC;YAEhB,wEAAwE;YACxE,IAAI,CAAC;gBACH,MAAM,QAAQ,GAAG,MAAM,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC;oBAC3C,MAAM,EAAE,uBAAuB;oBAC/B,WAAW,EAAE,6BAA6B;oBAC1C,UAAU,EAAE,8CAA8C;iBAC3D,CAAC,CAAC;gBAEH,OAAO,CAAC,CAAC,QAAQ,CAAC;YACpB,CAAC;YAAC,OAAO,UAAU,EAAE,CAAC;gBACpB,6CAA6C;gBAC7C,OAAO,CAAC,GAAG,CAAC,qDAAqD,EAAE,UAAU,CAAC,CAAC;gBAE/E,MAAM,QAAQ,GAAG,MAAM,IAAA,oBAAK,EAAC,0CAA0C,EAAE;oBACvE,MAAM,EAAE,KAAK;oBACb,OAAO,EAAE;wBACP,eAAe,EAAE,OAAO;qBACzB;iBACF,CAAC,CAAC;gBAEH,OAAO,QAAQ,CAAC,EAAE,CAAC;YACrB,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC,CAAC;YAC3D,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAIM,KAAK,CAAC,kBAAkB;QAC7B,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACxB,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,CAAC;YAChD,OAAO;QACT,CAAC;QAED,4BAA4B;QAC5B,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,EAAE,CAAC;YAC9B,OAAO,CAAC,KAAK,CAAC,+CAA+C,CAAC,CAAC;YAC/D,IAAI,CAAC,yBAAyB,EAAE,CAAC;YACjC,OAAO;QACT,CAAC;QAED,IAAI,CAAC;YACH,6BAA6B;YAC7B,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,WAAW,CAAC;gBAClD,UAAU,EAAE,IAAI,CAAC,WAAW;aAC7B,CAAC,CAAC;YAEH,wBAAwB;YACxB,IAAI,CAAC,sBAAsB,EAAE,CAAC;YAE9B,8CAA8C;YAC9C,OAAO,CAAC,GAAG,CAAC,4CAA4C,CAAC,CAAC;YAC1D,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;YAEjC,kBAAkB;YAClB,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC;YAClC,IAAI,CAAC;gBACH,IAAI,CAAC,SAAS,GAAG,IAAI,kBAAY,CAAC;oBAChC,QAAQ,EAAE,CAAC;oBACX,UAAU,EAAE,IAAI,CAAC,WAAW;oBAC5B,SAAS,EAAE,KAAK,CAAC,aAAa;iBAC/B,CAAC,CAAC;gBAEH,sDAAsD;gBACtD,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,CAAC;gBAC1D,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;YAC7B,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;gBAElD,0CAA0C;gBAC1C,iBAAM,CAAC,cAAc,CAAC;oBACpB,IAAI,EAAE,OAAO;oBACb,KAAK,EAAE,iBAAiB;oBACxB,OAAO,EAAE,iCAAiC;oBAC1C,MAAM,EAAE,4EAA4E;oBACpF,OAAO,EAAE,CAAC,IAAI,CAAC;oBACf,SAAS,EAAE,CAAC;iBACb,CAAC,CAAC;gBAEH,+BAA+B;gBAC/B,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;oBACrB,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;gBAC3B,CAAC;YACH,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;QACxD,CAAC;IACH,CAAC;IAEO,sBAAsB;QAC5B,IAAI,CAAC,IAAI,CAAC,WAAW;YAAE,OAAO;QAE9B,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,EAAE,SAAS,EAAyB,EAAE,EAAE;YACnE,OAAO,CAAC,GAAG,CAAC,2BAA2B,SAAS,EAAE,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAY,EAAE,EAAE;YAC5C,OAAO,CAAC,KAAK,CAAC,sBAAsB,EAAE,KAAK,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,IAAY,EAAE,MAAc,EAAE,EAAE;YAC5D,OAAO,CAAC,GAAG,CAAC,iBAAiB,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;YAC7C,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;QAC9B,CAAC,CAAC,CAAC;QAEH,kEAAkE;QAClE,IAAI,qBAAqB,GAAG,CAAC,CAAC;QAC9B,IAAI,mBAAmB,GAAG,CAAC,CAAC;QAC5B,IAAI,uBAAuB,GAAG,EAAE,CAAC;QACjC,MAAM,uBAAuB,GAAG,EAAE,CAAC,CAAC,sDAAsD;QAC1F,MAAM,qBAAqB,GAAG,GAAG,CAAC,CAAC,qDAAqD;QAExF,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC,UAA8B,EAAE,EAAE;YACnE,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC;gBACrB,OAAO;YACT,CAAC;YAED,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAEvB,yCAAyC;YACzC,IAAI,CAAC,uBAAuB,EAAE,CAAC;YAE/B,IAAI,UAAU,CAAC,YAAY,KAAK,mBAAmB,EAAE,CAAC;gBACpD,+DAA+D;gBAC/D,uBAAuB,GAAG,UAAU,CAAC,IAAI,CAAC;gBAE1C,kFAAkF;gBAClF,IAAI,GAAG,GAAG,qBAAqB,GAAG,uBAAuB,EAAE,CAAC;oBAC1D,IAAI,CAAC,oBAAoB,CAAC,uBAAuB,EAAE,IAAI,CAAC,CAAC;oBACzD,qBAAqB,GAAG,GAAG,CAAC;gBAC9B,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,6EAA6E;gBAC7E,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,GAAG,GAAG,CAAC,CAAC;gBAElD,sDAAsD;gBACtD,IAAI,IAAI,CAAC,gBAAgB,CAAC,MAAM,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;oBACnD,uCAAuC;oBACvC,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;oBAElD,yCAAyC;oBACzC,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC;wBACxD,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;oBACvE,CAAC;oBAED,yBAAyB;oBACzB,IAAI,CAAC,gBAAgB,GAAG,EAAE,CAAC;gBAC7B,CAAC;gBAED,sCAAsC;gBACtC,uBAAuB,GAAG,EAAE,CAAC;gBAE7B,sDAAsD;gBACtD,IAAI,GAAG,GAAG,mBAAmB,GAAG,qBAAqB,EAAE,CAAC;oBACtD,MAAM,cAAc,GAAG,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;oBACxE,IAAI,CAAC,oBAAoB,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC;oBACjD,mBAAmB,GAAG,GAAG,CAAC;gBAC5B,CAAC;YACH,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAKO,oBAAoB,CAAC,IAAY,EAAE,SAAkB;QAC3D,gDAAgD;QAChD,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE,EAAE,CAAC;YACtD,MAAM,OAAO,GAAG,wBAAa,CAAC,aAAa,EAAE,CAAC;YAC9C,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC;gBAAE,OAAO,CAAC,uBAAuB;YACzD,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,kCAAkC;QAClE,CAAC;QAED,iCAAiC;QACjC,MAAM,OAAO,GAAG,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC;QAEpC,mEAAmE;QACnE,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE,EAAE,CAAC;YACnC,gCAAgC;YAChC,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,mBAAmB,EAAE,OAAO,CAAC,CAAC;QACjE,CAAC;IACH,CAAC;IAEM,iBAAiB;QACtB,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;YACzB,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC,CAAC;YAC3C,OAAO;QACT,CAAC;QAED,qBAAqB;QACrB,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;YACtB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACxB,CAAC;QAED,mCAAmC;QACnC,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACrB,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;QAC3B,CAAC;QAED,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;IAC9B,CAAC;IAEM,aAAa;QAClB,sDAAsD;QACtD,OAAO,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IAC1D,CAAC;IAED,+BAA+B;IACxB,WAAW;QAChB,OAAO,IAAI,CAAC,cAAc,CAAC;IAC7B,CAAC;IAEM,eAAe;QACpB,kDAAkD;QAClD,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;QACrB,IAAI,CAAC,gBAAgB,GAAG,EAAE,CAAC;QAC3B,IAAI,CAAC,oBAAoB,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;IACvC,CAAC;IAED;;;OAGG;IACI,uBAAuB;QAC5B,kEAAkE;QAClE,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,IAAI,GAAG,GAAG,IAAI,CAAC,eAAe,GAAG,KAAK,EAAE,CAAC;YACvC,OAAO;QACT,CAAC;QAED,IAAI,CAAC,eAAe,GAAG,GAAG,CAAC;QAE3B,kDAAkD;QAClD,IAAI,IAAI,CAAC,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACrC,2CAA2C;YAC3C,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAClD,IAAI,CAAC,gBAAgB,GAAG,EAAE,CAAC;YAE3B,yCAAyC;YACzC,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC;gBACxD,oCAAoC;gBACpC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;YACvE,CAAC;QACH,CAAC;QAED,wCAAwC;QACxC,IAAI,MAAM,CAAC,EAAE,EAAE,CAAC;YACd,IAAI,CAAC;gBACH,MAAM,CAAC,EAAE,EAAE,CAAC;YACd,CAAC;YAAC,OAAO,CAAC,EAAE,CAAC;gBACX,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAC;YACpD,CAAC;QACH,CAAC;IACH,CAAC;CACF;AA9UD,kDA8UC"}