{"version": 3, "file": "main.js", "sourceRoot": "", "sources": ["../../src/electron/main.ts"], "names": [], "mappings": ";;;;;AAAA,uCAAuE;AAEvE,iDAA8C;AAC9C,+DAA4D;AAC5D,iDAA8C;AAC9C,uDAAoD;AACpD,mCAAgC;AAChC,oEAAmC;AAEnC,4CAA4C;AAC5C,IAAA,eAAM,GAAE,CAAC;AAwBT,+CAA+C;AAC/C,MAAM,KAAK,GAAG,IAAI,wBAAK,EAOnB,CAAC;AAEL,2CAA2C;AAC3C,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE,CAAC;IAC3B,KAAK,CAAC,GAAG,CAAC,UAAU,EAAE;QACpB,MAAM,EAAE,EAAE;QACV,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE,EAAE;KACtB,CAAC,CAAC;AACL,CAAC;AAED,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,aAAa,CAAC,EAAE,CAAC;IAC9B,KAAK,CAAC,GAAG,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;AAClC,CAAC;AAED,2CAA2C;AAC3C,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC;IAC1B,KAAK,CAAC,GAAG,CAAC,SAAS,EAAE;QACnB,MAAM,EAAE,EAAE;QACV,UAAU,EAAE,EAAE;KACf,CAAC,CAAC;AACL,CAAC;AAED,MAAM,WAAW;IAQf;QAHQ,gBAAW,GAAW,YAAY,CAAC;QACnC,gBAAW,GAAY,KAAK,CAAC,GAAG,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;QAG7D,IAAI,CAAC,YAAY,GAAG,IAAI,2BAAY,CAAC,IAAI,CAAC,CAAC;QAC3C,IAAI,CAAC,mBAAmB,GAAG,IAAI,yCAAmB,EAAE,CAAC;QACrD,IAAI,CAAC,YAAY,GAAG,IAAI,2BAAY,CAAC,IAAI,CAAC,CAAC;QAC3C,IAAI,CAAC,eAAe,GAAG,IAAI,iCAAe,CAAC,IAAI,CAAC,CAAC;QAEjD,IAAI,CAAC,cAAc,EAAE,CAAC;QACtB,IAAI,CAAC,gBAAgB,EAAE,CAAC;IAC1B,CAAC;IAEO,cAAc;QACpB,4CAA4C;QAC5C,cAAG,CAAC,EAAE,CAAC,OAAO,EAAE,KAAK,IAAI,EAAE;YACzB,IAAI,CAAC,YAAY,CAAC,YAAY,EAAE,CAAC;YACjC,IAAI,CAAC,eAAe,CAAC,uBAAuB,EAAE,CAAC;YAE/C,oEAAoE;YACpE,UAAU,CAAC,KAAK,IAAI,EAAE;gBACpB,MAAM,IAAI,CAAC,mBAAmB,CAAC,kBAAkB,EAAE,CAAC;YACtD,CAAC,EAAE,IAAI,CAAC,CAAC;QACX,CAAC,CAAC,CAAC;QAEH,oDAAoD;QACpD,cAAG,CAAC,EAAE,CAAC,mBAAmB,EAAE,GAAG,EAAE;YAC/B,IAAI,OAAO,CAAC,QAAQ,KAAK,QAAQ,EAAE,CAAC;gBAClC,cAAG,CAAC,IAAI,EAAE,CAAC;YACb,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,cAAG,CAAC,EAAE,CAAC,UAAU,EAAE,GAAG,EAAE;YACtB,2EAA2E;YAC3E,IAAI,wBAAa,CAAC,aAAa,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC/C,IAAI,CAAC,YAAY,CAAC,YAAY,EAAE,CAAC;YACnC,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,gCAAgC;QAChC,cAAG,CAAC,EAAE,CAAC,WAAW,EAAE,GAAG,EAAE;YACvB,yBAAc,CAAC,aAAa,EAAE,CAAC;YAC/B,IAAI,CAAC,mBAAmB,CAAC,iBAAiB,EAAE,CAAC;QAC/C,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,gBAAgB;QACtB,gDAAgD;QAChD,kBAAO,CAAC,MAAM,CAAC,gBAAgB,EAAE,GAAG,EAAE;YACpC,OAAO,IAAI,CAAC,mBAAmB,CAAC,aAAa,EAAE,CAAC;QAClD,CAAC,CAAC,CAAC;QAEH,kBAAO,CAAC,MAAM,CAAC,kBAAkB,EAAE,GAAG,EAAE;YACtC,IAAI,CAAC,eAAe,EAAE,CAAC;QACzB,CAAC,CAAC,CAAC;QAEH,kBAAO,CAAC,MAAM,CAAC,mBAAmB,EAAE,KAAK,IAAI,EAAE;YAC7C,OAAO,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACvC,CAAC,CAAC,CAAC;QAEH,kBAAO,CAAC,MAAM,CAAC,iBAAiB,EAAE,KAAK,EAAE,CAAC,EAAE,QAAgB,EAAE,EAAE;YAC9D,OAAO,MAAM,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,kBAAO,CAAC,MAAM,CAAC,mBAAmB,EAAE,GAAG,EAAE;YACvC,OAAO,IAAI,CAAC,eAAe,EAAE,CAAC;QAChC,CAAC,CAAC,CAAC;QAEH,kBAAO,CAAC,MAAM,CAAC,oBAAoB,EAAE,CAAC,CAAC,EAAE,QAAsB,EAAE,EAAE;YACjE,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC;QAEH,kBAAO,CAAC,MAAM,CAAC,eAAe,EAAE,GAAG,EAAE;YACnC,IAAI,CAAC,YAAY,CAAC,gBAAgB,EAAE,CAAC;QACvC,CAAC,CAAC,CAAC;QAEH,qBAAqB;QACrB,kBAAO,CAAC,MAAM,CAAC,cAAc,EAAE,GAAG,EAAE;YAClC,OAAO,KAAK,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC;QAEH,kBAAO,CAAC,MAAM,CAAC,eAAe,EAAE,CAAC,MAAM,EAAE,IAA4C,EAAE,EAAE;YACvF,IAAI,CAAC;gBACH,KAAK,CAAC,GAAG,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;gBAE3B,qCAAqC;gBACrC,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBAC5C,IAAI,CAAC,mBAAmB,CAAC,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;gBAEvD,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;YAC3B,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;gBAC/C,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,yBAAyB,EAAE,CAAC;YAC9D,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,kBAAO,CAAC,MAAM,CAAC,wBAAwB,EAAE,KAAK,EAAE,MAAM,EAAE,MAAe,EAAE,EAAE;YACzE,IAAI,CAAC;gBACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;gBAC9D,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC;YAC7B,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;gBACzD,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,kCAAkC,EAAE,CAAC;YACvE,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,kBAAO,CAAC,MAAM,CAAC,4BAA4B,EAAE,KAAK,EAAE,MAAM,EAAE,MAAe,EAAE,EAAE;YAC7E,IAAI,CAAC;gBACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;gBACrE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC;YAC7B,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,sCAAsC,EAAE,KAAK,CAAC,CAAC;gBAC7D,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,sCAAsC,EAAE,CAAC;YAC3E,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,kBAAO,CAAC,MAAM,CAAC,gBAAgB,EAAE,GAAG,EAAE;YACpC,IAAI,CAAC;gBACH,KAAK,CAAC,GAAG,CAAC,SAAS,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,UAAU,EAAE,EAAE,EAAE,CAAC,CAAC;gBACrD,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;YAC3B,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAC;gBACjD,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,0BAA0B,EAAE,CAAC;YAC/D,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,yCAAyC;QACzC,kBAAO,CAAC,MAAM,CAAC,oBAAoB,EAAE,GAAG,EAAE;YACxC,IAAI,CAAC,mBAAmB,CAAC,iBAAiB,EAAE,CAAC;YAE7C,2CAA2C;YAC3C,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,aAAa,EAAE,CAAC;YACrD,IAAI,UAAU,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE,EAAE,CAAC;gBAC5C,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,0BAA0B,EAAE,EAAE,WAAW,EAAE,KAAK,EAAE,CAAC,CAAC;YAClF,CAAC;YAED,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;QAC3B,CAAC,CAAC,CAAC;QAEH,yCAAyC;QACzC,kBAAO,CAAC,MAAM,CAAC,qBAAqB,EAAE,KAAK,IAAI,EAAE;YAC/C,MAAM,IAAI,CAAC,mBAAmB,CAAC,kBAAkB,EAAE,CAAC;YAEpD,2CAA2C;YAC3C,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,aAAa,EAAE,CAAC;YACrD,IAAI,UAAU,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE,EAAE,CAAC;gBAC5C,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,0BAA0B,EAAE,EAAE,WAAW,EAAE,IAAI,EAAE,CAAC,CAAC;YACjF,CAAC;YAED,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;QAC3B,CAAC,CAAC,CAAC;QAEH,4CAA4C;QAC5C,kBAAO,CAAC,MAAM,CAAC,cAAc,EAAE,GAAG,EAAE;YAClC,OAAO,EAAE,WAAW,EAAE,IAAI,CAAC,mBAAmB,CAAC,WAAW,EAAE,EAAE,CAAC;QACjE,CAAC,CAAC,CAAC;QAEH,6CAA6C;QAC7C,kBAAO,CAAC,MAAM,CAAC,aAAa,EAAE,GAAG,EAAE;YACjC,IAAI,CAAC,UAAU,EAAE,CAAC;YAClB,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;QAC3B,CAAC,CAAC,CAAC;IACL,CAAC;IAEM,aAAa;QAClB,OAAO,IAAI,CAAC,YAAY,CAAC,aAAa,EAAE,CAAC;IAC3C,CAAC;IAEM,cAAc;QACnB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC1B,CAAC;IAEM,cAAc,CAAC,KAAc;QAClC,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;QACzB,KAAK,CAAC,GAAG,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;IAClC,CAAC;IAEM,OAAO,CAAC,IAAY;QACzB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;QACxB,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;QACxC,IAAI,UAAU,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE,EAAE,CAAC;YAC5C,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;QAChD,CAAC;IACH,CAAC;IAEM,eAAe;QACpB,IAAI,CAAC,mBAAmB,CAAC,eAAe,EAAE,CAAC;QAC3C,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;QACxC,IAAI,UAAU,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE,EAAE,CAAC;YAC5C,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;QACpD,CAAC;IACH,CAAC;IAEM,KAAK,CAAC,gBAAgB;QAC3B,MAAM,UAAU,GAAG,IAAI,CAAC,mBAAmB,CAAC,aAAa,EAAE,CAAC;QAC5D,OAAO,MAAM,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC;IAC9D,CAAC;IAEM,KAAK,CAAC,cAAc,CAAC,QAAgB;QAC1C,OAAO,MAAM,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;IAC1D,CAAC;IAEM,eAAe;QACpB,OAAO,KAAK,CAAC,GAAG,CAAC,UAAU,CAAiB,CAAC;IAC/C,CAAC;IAEM,gBAAgB,CAAC,QAAsB;QAC5C,KAAK,CAAC,GAAG,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;IAClC,CAAC;IAED;;;OAGG;IACI,UAAU;QACf,OAAO,CAAC,GAAG,CAAC,2BAA2B,CAAC,CAAC;QAEzC,2CAA2C;QAC3C,IAAI,CAAC,mBAAmB,CAAC,iBAAiB,EAAE,CAAC;QAE7C,wCAAwC;QACxC,IAAI,MAAM,CAAC,EAAE,EAAE,CAAC;YACd,IAAI,CAAC;gBACH,MAAM,CAAC,EAAE,EAAE,CAAC;gBACZ,OAAO,CAAC,GAAG,CAAC,0CAA0C,CAAC,CAAC;YAC1D,CAAC;YAAC,OAAO,CAAC,EAAE,CAAC;gBACX,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAC;YACpD,CAAC;QACH,CAAC;QAED,kBAAkB;QAClB,cAAG,CAAC,QAAQ,EAAE,CAAC;QACf,cAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IACd,CAAC;CACF;AAWD,wCAAwC;AACxC,IAAI,WAAW,EAAE,CAAC"}