"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ShortcutsHelper = void 0;
const electron_1 = require("electron");
class ShortcutsHelper {
    constructor(appState) {
        this.appState = appState;
    }
    registerGlobalShortcuts() {
        // Toggle window visibility (cmd+B)
        electron_1.globalShortcut.register('CommandOrControl+B', () => {
            console.log('Toggling window visibility');
            // Directly call the toggleMainWindow method instead of sending a message
            this.appState.windowHelper.toggleMainWindow();
        });
        // Extract questions from transcript (cmd+H)
        electron_1.globalShortcut.register('CommandOrControl+H', async () => {
            console.log('Extracting questions from transcript');
            try {
                const questions = await this.appState.extractQuestions();
                const mainWindow = this.appState.getMainWindow();
                if (mainWindow && !mainWindow.isDestroyed()) {
                    mainWindow.webContents.send('questions-extracted', questions);
                }
            }
            catch (error) {
                console.error('Error extracting questions:', error);
            }
        });
        // Clear transcript (cmd+R)
        electron_1.globalShortcut.register('CommandOrControl+R', () => {
            console.log('Clearing transcript');
            this.appState.clearTranscript();
        });
        // Generate answer for selected question (cmd+Enter)
        electron_1.globalShortcut.register('CommandOrControl+Enter', async () => {
            console.log('Generating answer for selected question');
            const mainWindow = this.appState.getMainWindow();
            if (mainWindow && !mainWindow.isDestroyed()) {
                mainWindow.webContents.send('generate-answer-request');
            }
        });
        // Window movement shortcuts (cmd+arrow keys)
        // Move window left (cmd+left arrow)
        electron_1.globalShortcut.register('CommandOrControl+Left', () => {
            console.log('Moving window left');
            this.appState.windowHelper.moveWindow('left');
        });
        // Move window right (cmd+right arrow)
        electron_1.globalShortcut.register('CommandOrControl+Right', () => {
            console.log('Moving window right');
            this.appState.windowHelper.moveWindow('right');
        });
        // Move window up (cmd+up arrow)
        electron_1.globalShortcut.register('CommandOrControl+Up', () => {
            console.log('Moving window up');
            this.appState.windowHelper.moveWindow('up');
        });
        // Move window down (cmd+down arrow)
        electron_1.globalShortcut.register('CommandOrControl+Down', () => {
            console.log('Moving window down');
            this.appState.windowHelper.moveWindow('down');
        });
        // Restart application (cmd+Q)
        electron_1.globalShortcut.register('CommandOrControl+Q', () => {
            console.log('Restarting application');
            this.appState.restartApp();
        });
        // Quit application (cmd+shift+Q)
        electron_1.globalShortcut.register('CommandOrControl+Shift+Q', () => {
            console.log('Quitting application');
            electron_1.app.quit();
        });
    }
    unregisterGlobalShortcuts() {
        electron_1.globalShortcut.unregisterAll();
    }
}
exports.ShortcutsHelper = ShortcutsHelper;
//# sourceMappingURL=ShortcutsHelper.js.map