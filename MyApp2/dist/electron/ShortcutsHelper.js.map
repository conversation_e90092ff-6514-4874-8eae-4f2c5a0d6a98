{"version": 3, "file": "ShortcutsHelper.js", "sourceRoot": "", "sources": ["../../src/electron/ShortcutsHelper.ts"], "names": [], "mappings": ";;;AAAA,uCAA+C;AAG/C,MAAa,eAAe;IAG1B,YAAY,QAAkB;QAC5B,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;IAC3B,CAAC;IAEM,uBAAuB;QAC5B,mCAAmC;QACnC,yBAAc,CAAC,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;YACjD,OAAO,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC;YAC1C,yEAAyE;YACzE,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,gBAAgB,EAAE,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,4CAA4C;QAC5C,yBAAc,CAAC,QAAQ,CAAC,oBAAoB,EAAE,KAAK,IAAI,EAAE;YACvD,OAAO,CAAC,GAAG,CAAC,sCAAsC,CAAC,CAAC;YACpD,IAAI,CAAC;gBACH,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,gBAAgB,EAAE,CAAC;gBACzD,MAAM,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,aAAa,EAAE,CAAC;gBACjD,IAAI,UAAU,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE,EAAE,CAAC;oBAC5C,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,qBAAqB,EAAE,SAAS,CAAC,CAAC;gBAChE,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;YACtD,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,2BAA2B;QAC3B,yBAAc,CAAC,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;YACjD,OAAO,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAAC;YACnC,IAAI,CAAC,QAAQ,CAAC,eAAe,EAAE,CAAC;QAClC,CAAC,CAAC,CAAC;QAEH,oDAAoD;QACpD,yBAAc,CAAC,QAAQ,CAAC,wBAAwB,EAAE,KAAK,IAAI,EAAE;YAC3D,OAAO,CAAC,GAAG,CAAC,yCAAyC,CAAC,CAAC;YACvD,MAAM,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,aAAa,EAAE,CAAC;YACjD,IAAI,UAAU,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE,EAAE,CAAC;gBAC5C,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;YACzD,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,6CAA6C;QAC7C,oCAAoC;QACpC,yBAAc,CAAC,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;YACpD,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC;YAClC,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,sCAAsC;QACtC,yBAAc,CAAC,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;YACrD,OAAO,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAAC;YACnC,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;QAEH,gCAAgC;QAChC,yBAAc,CAAC,QAAQ,CAAC,qBAAqB,EAAE,GAAG,EAAE;YAClD,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;YAChC,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;QAEH,oCAAoC;QACpC,yBAAc,CAAC,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;YACpD,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC;YAClC,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,8BAA8B;QAC9B,yBAAc,CAAC,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;YACjD,OAAO,CAAC,GAAG,CAAC,wBAAwB,CAAC,CAAC;YACtC,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE,CAAC;QAC7B,CAAC,CAAC,CAAC;QAEH,iCAAiC;QACjC,yBAAc,CAAC,QAAQ,CAAC,0BAA0B,EAAE,GAAG,EAAE;YACvD,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAC;YACpC,cAAG,CAAC,IAAI,EAAE,CAAC;QACb,CAAC,CAAC,CAAC;IACL,CAAC;IAEM,yBAAyB;QAC9B,yBAAc,CAAC,aAAa,EAAE,CAAC;IACjC,CAAC;CACF;AArFD,0CAqFC"}