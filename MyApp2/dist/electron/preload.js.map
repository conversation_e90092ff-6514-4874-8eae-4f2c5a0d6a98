{"version": 3, "file": "preload.js", "sourceRoot": "", "sources": ["../../src/electron/preload.ts"], "names": [], "mappings": ";;AAAA,uCAAsD;AAEtD,iDAAiD;AACjD,wBAAa,CAAC,iBAAiB,CAAC,aAAa,EAAE;IAC7C,uBAAuB;IACvB,aAAa,EAAE,GAAG,EAAE,CAAC,sBAAW,CAAC,MAAM,CAAC,gBAAgB,CAAC;IACzD,eAAe,EAAE,GAAG,EAAE,CAAC,sBAAW,CAAC,MAAM,CAAC,kBAAkB,CAAC;IAC7D,kBAAkB,EAAE,GAAG,EAAE,CAAC,sBAAW,CAAC,MAAM,CAAC,qBAAqB,CAAC;IACnE,iBAAiB,EAAE,GAAG,EAAE,CAAC,sBAAW,CAAC,MAAM,CAAC,oBAAoB,CAAC;IACjE,WAAW,EAAE,GAAG,EAAE,CAAC,sBAAW,CAAC,MAAM,CAAC,cAAc,CAAC;IACrD,kBAAkB,EAAE,CAAC,QAA8D,EAAE,EAAE;QACrF,sBAAW,CAAC,EAAE,CAAC,mBAAmB,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;QACjE,OAAO,GAAG,EAAE,CAAC,sBAAW,CAAC,kBAAkB,CAAC,mBAAmB,CAAC,CAAC;IACnE,CAAC;IACD,mBAAmB,EAAE,CAAC,QAAoB,EAAE,EAAE;QAC5C,sBAAW,CAAC,EAAE,CAAC,oBAAoB,EAAE,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC,CAAC;QACvD,OAAO,GAAG,EAAE,CAAC,sBAAW,CAAC,kBAAkB,CAAC,oBAAoB,CAAC,CAAC;IACpE,CAAC;IAED,4CAA4C;IAC5C,gBAAgB,EAAE,GAAG,EAAE,CAAC,sBAAW,CAAC,MAAM,CAAC,mBAAmB,CAAC;IAC/D,oBAAoB,EAAE,CAAC,QAAuC,EAAE,EAAE;QAChE,sBAAW,CAAC,EAAE,CAAC,qBAAqB,EAAE,CAAC,CAAC,EAAE,SAAS,EAAE,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC;QAC7E,OAAO,GAAG,EAAE,CAAC,sBAAW,CAAC,kBAAkB,CAAC,qBAAqB,CAAC,CAAC;IACrE,CAAC;IACD,cAAc,EAAE,CAAC,QAAgB,EAAE,EAAE,CAAC,sBAAW,CAAC,MAAM,CAAC,iBAAiB,EAAE,QAAQ,CAAC;IACrF,uBAAuB,EAAE,CAAC,QAAoB,EAAE,EAAE;QAChD,sBAAW,CAAC,EAAE,CAAC,yBAAyB,EAAE,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC,CAAC;QAC5D,OAAO,GAAG,EAAE,CAAC,sBAAW,CAAC,kBAAkB,CAAC,yBAAyB,CAAC,CAAC;IACzE,CAAC;IAED,gBAAgB;IAChB,eAAe,EAAE,GAAG,EAAE,CAAC,sBAAW,CAAC,MAAM,CAAC,mBAAmB,CAAC;IAC9D,gBAAgB,EAAE,CAAC,QAIlB,EAAE,EAAE,CAAC,sBAAW,CAAC,MAAM,CAAC,oBAAoB,EAAE,QAAQ,CAAC;IAExD,qBAAqB;IACrB,UAAU,EAAE,GAAG,EAAE,CAAC,sBAAW,CAAC,MAAM,CAAC,cAAc,CAAC;IACpD,WAAW,EAAE,CAAC,IAA4C,EAAE,EAAE,CAAC,sBAAW,CAAC,MAAM,CAAC,eAAe,EAAE,IAAI,CAAC;IACxG,oBAAoB,EAAE,CAAC,MAAe,EAAE,EAAE,CAAC,sBAAW,CAAC,MAAM,CAAC,wBAAwB,EAAE,MAAM,CAAC;IAC/F,wBAAwB,EAAE,CAAC,MAAe,EAAE,EAAE,CAAC,sBAAW,CAAC,MAAM,CAAC,4BAA4B,EAAE,MAAM,CAAC;IACvG,YAAY,EAAE,GAAG,EAAE,CAAC,sBAAW,CAAC,MAAM,CAAC,gBAAgB,CAAC;IAExD,oBAAoB;IACpB,YAAY,EAAE,GAAG,EAAE,CAAC,sBAAW,CAAC,MAAM,CAAC,eAAe,CAAC;IACvD,cAAc,EAAE,CAAC,QAAoB,EAAE,EAAE;QACvC,sBAAW,CAAC,EAAE,CAAC,eAAe,EAAE,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC,CAAC;QAClD,OAAO,GAAG,EAAE,CAAC,sBAAW,CAAC,kBAAkB,CAAC,eAAe,CAAC,CAAC;IAC/D,CAAC;IACD,SAAS,EAAE,CAAC,QAAgC,EAAE,EAAE;QAC9C,sBAAW,CAAC,EAAE,CAAC,UAAU,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;QACxD,OAAO,GAAG,EAAE,CAAC,sBAAW,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC;IAC1D,CAAC;IAED,mBAAmB;IACnB,wBAAwB,EAAE,CAAC,QAAkD,EAAE,EAAE;QAC/E,sBAAW,CAAC,EAAE,CAAC,0BAA0B,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;QACxE,OAAO,GAAG,EAAE,CAAC,sBAAW,CAAC,kBAAkB,CAAC,0BAA0B,CAAC,CAAC;IAC1E,CAAC;IAED,4BAA4B;IAC5B,UAAU,EAAE,GAAG,EAAE,CAAC,sBAAW,CAAC,MAAM,CAAC,aAAa,CAAC;CACpD,CAAC,CAAC"}