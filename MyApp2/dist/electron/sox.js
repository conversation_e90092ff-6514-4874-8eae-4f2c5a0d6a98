"use strict";
// This code is a simplified and typed version adapted from the 'node-record-lpcm16' project by <PERSON>.
// Original source code: https://github.com/gillesdemey/node-record-lpcm16
Object.defineProperty(exports, "__esModule", { value: true });
exports.SoxRecording = void 0;
const assert_1 = require("assert");
const child_process_1 = require("child_process");
const stream_1 = require("stream");
const debug = !!process.env.DEBUG && process.env.DEBUG.indexOf("record") !== -1
    ? console.debug
    : () => { };
class SoxRecording {
    constructor(options = {}) {
        const defaults = {
            sampleRate: 16000,
            channels: 1,
            compress: false,
            threshold: 0.5,
            silence: "1.0",
            recorder: "sox",
            endOnSilence: false,
            audioType: "wav",
        };
        this.options = Object.assign(defaults, options);
        debug("Started recording");
        debug(this.options);
        return this.start();
    }
    start() {
        const cmd = "sox";
        const args = [
            "--default-device",
            "--no-show-progress", // show no progress
            "--rate",
            this.options.sampleRate.toString(), // sample rate
            "--channels",
            this.options.channels.toString(), // channels
            "--encoding",
            "signed-integer", // sample encoding
            "--bits",
            "16", // precision (bits)
            "--type",
            this.options.audioType, // audio type
            "-", // pipe
        ];
        debug(` ${cmd} ${args.join(" ")}`);
        const cp = (0, child_process_1.spawn)(cmd, args, {
            stdio: "pipe",
        });
        const rec = cp.stdout;
        const err = cp.stderr;
        this.process = cp; // expose child process
        this.soxStream = cp.stdout; // expose output stream
        cp.on("close", (code) => {
            if (code === 0)
                return;
            rec?.emit("error", `${cmd} has exited with error code ${code}.\n\nEnable debugging with the environment variable debug=record.`);
        });
        err?.on("data", (chunk) => {
            debug(`STDERR: ${chunk}`);
        });
        rec?.on("data", (chunk) => {
            debug(`Recording ${chunk.length} bytes`);
        });
        rec?.on("end", () => {
            debug("Recording ended");
        });
        return this;
    }
    stop() {
        (0, assert_1.ok)(this.process, "Recording not yet started");
        this.process.kill();
    }
    pause() {
        (0, assert_1.ok)(this.process, "Recording not yet started");
        this.process.kill("SIGSTOP");
        this.soxStream?.pause();
        debug("Paused recording");
    }
    resume() {
        (0, assert_1.ok)(this.process, "Recording not yet started");
        this.process.kill("SIGCONT");
        this.soxStream?.resume();
        debug("Resumed recording");
    }
    isPaused() {
        (0, assert_1.ok)(this.process, "Recording not yet started");
        return this.soxStream?.isPaused();
    }
    stream() {
        (0, assert_1.ok)(this?.soxStream, "Recording not yet started");
        return stream_1.Readable.toWeb(this?.soxStream);
    }
}
exports.SoxRecording = SoxRecording;
//# sourceMappingURL=sox.js.map