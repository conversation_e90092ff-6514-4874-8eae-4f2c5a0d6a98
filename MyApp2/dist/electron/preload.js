"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const electron_1 = require("electron");
// Define the API exposed to the renderer process
electron_1.contextBridge.exposeInMainWorld('electronAPI', {
    // Transcript functions
    getTranscript: () => electron_1.ipcRenderer.invoke('get-transcript'),
    clearTranscript: () => electron_1.ipcRenderer.invoke('clear-transcript'),
    startTranscription: () => electron_1.ipcRenderer.invoke('start-transcription'),
    stopTranscription: () => electron_1.ipcRenderer.invoke('stop-transcription'),
    isRecording: () => electron_1.ipcRenderer.invoke('is-recording'),
    onTranscriptUpdate: (callback) => {
        electron_1.ipcRenderer.on('transcript-update', (_, data) => callback(data));
        return () => electron_1.ipcRenderer.removeAllListeners('transcript-update');
    },
    onTranscriptCleared: (callback) => {
        electron_1.ipcRenderer.on('transcript-cleared', () => callback());
        return () => electron_1.ipcRenderer.removeAllListeners('transcript-cleared');
    },
    // Question extraction and answer generation
    extractQuestions: () => electron_1.ipcRenderer.invoke('extract-questions'),
    onQuestionsExtracted: (callback) => {
        electron_1.ipcRenderer.on('questions-extracted', (_, questions) => callback(questions));
        return () => electron_1.ipcRenderer.removeAllListeners('questions-extracted');
    },
    generateAnswer: (question) => electron_1.ipcRenderer.invoke('generate-answer', question),
    onGenerateAnswerRequest: (callback) => {
        electron_1.ipcRenderer.on('generate-answer-request', () => callback());
        return () => electron_1.ipcRenderer.removeAllListeners('generate-answer-request');
    },
    // User settings
    getUserSettings: () => electron_1.ipcRenderer.invoke('get-user-settings'),
    saveUserSettings: (settings) => electron_1.ipcRenderer.invoke('save-user-settings', settings),
    // API key management
    getApiKeys: () => electron_1.ipcRenderer.invoke('get-api-keys'),
    saveApiKeys: (keys) => electron_1.ipcRenderer.invoke('save-api-keys', keys),
    testOpenAIConnection: (apiKey) => electron_1.ipcRenderer.invoke('test-openai-connection', apiKey),
    testAssemblyAIConnection: (apiKey) => electron_1.ipcRenderer.invoke('test-assemblyai-connection', apiKey),
    clearApiKeys: () => electron_1.ipcRenderer.invoke('clear-api-keys'),
    // Window management
    toggleWindow: () => electron_1.ipcRenderer.invoke('toggle-window'),
    onToggleWindow: (callback) => {
        electron_1.ipcRenderer.on('toggle-window', () => callback());
        return () => electron_1.ipcRenderer.removeAllListeners('toggle-window');
    },
    onSetView: (callback) => {
        electron_1.ipcRenderer.on('set-view', (_, view) => callback(view));
        return () => electron_1.ipcRenderer.removeAllListeners('set-view');
    },
    // Recording status
    onRecordingStatusChanged: (callback) => {
        electron_1.ipcRenderer.on('recording-status-changed', (_, data) => callback(data));
        return () => electron_1.ipcRenderer.removeAllListeners('recording-status-changed');
    },
    // App restart functionality
    restartApp: () => electron_1.ipcRenderer.invoke('restart-app')
});
//# sourceMappingURL=preload.js.map