{"version": 3, "file": "sox.js", "sourceRoot": "", "sources": ["../../src/electron/sox.ts"], "names": [], "mappings": ";AAAA,8GAA8G;AAC9G,0EAA0E;;;AAE1E,mCAAsC;AACtC,iDAAoD;AACpD,mCAAkC;AAalC,MAAM,KAAK,GACT,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,IAAI,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;IAC/D,CAAC,CAAC,OAAO,CAAC,KAAK;IACf,CAAC,CAAC,GAAG,EAAE,GAAE,CAAC,CAAC;AAEf,MAAa,YAAY;IAKvB,YAAY,UAAwC,EAAE;QACpD,MAAM,QAAQ,GAAG;YACf,UAAU,EAAE,KAAK;YACjB,QAAQ,EAAE,CAAC;YACX,QAAQ,EAAE,KAAK;YACf,SAAS,EAAE,GAAG;YACd,OAAO,EAAE,KAAK;YACd,QAAQ,EAAE,KAAK;YACf,YAAY,EAAE,KAAK;YACnB,SAAS,EAAE,KAAK;SACjB,CAAC;QAEF,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,QAAQ,EAAE,OAAO,CAAwB,CAAC;QAEvE,KAAK,CAAC,mBAAmB,CAAC,CAAC;QAC3B,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAEpB,OAAO,IAAI,CAAC,KAAK,EAAE,CAAC;IACtB,CAAC;IAED,KAAK;QACH,MAAM,GAAG,GAAG,KAAK,CAAC;QAClB,MAAM,IAAI,GAAG;YACX,kBAAkB;YAClB,oBAAoB,EAAE,mBAAmB;YACzC,QAAQ;YACR,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,QAAQ,EAAE,EAAE,cAAc;YAClD,YAAY;YACZ,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,WAAW;YAC7C,YAAY;YACZ,gBAAgB,EAAE,kBAAkB;YACpC,QAAQ;YACR,IAAI,EAAE,mBAAmB;YACzB,QAAQ;YACR,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,aAAa;YACrC,GAAG,EAAE,OAAO;SACb,CAAC;QACF,KAAK,CAAC,IAAI,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QAEnC,MAAM,EAAE,GAAG,IAAA,qBAAK,EAAC,GAAG,EAAE,IAAI,EAAE;YAC1B,KAAK,EAAE,MAAM;SACd,CAAC,CAAC;QACH,MAAM,GAAG,GAAG,EAAE,CAAC,MAAM,CAAC;QACtB,MAAM,GAAG,GAAG,EAAE,CAAC,MAAM,CAAC;QAEtB,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC,CAAC,uBAAuB;QAC1C,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC,MAAM,CAAC,CAAC,uBAAuB;QAEnD,EAAE,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,IAAI,EAAE,EAAE;YACtB,IAAI,IAAI,KAAK,CAAC;gBAAE,OAAO;YACvB,GAAG,EAAE,IAAI,CACP,OAAO,EACP,GAAG,GAAG,+BAA+B,IAAI,mEAAmE,CAC7G,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,GAAG,EAAE,EAAE,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,EAAE;YACxB,KAAK,CAAC,WAAW,KAAK,EAAE,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC;QAEH,GAAG,EAAE,EAAE,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,EAAE;YACxB,KAAK,CAAC,aAAa,KAAK,CAAC,MAAM,QAAQ,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;QAEH,GAAG,EAAE,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE;YAClB,KAAK,CAAC,iBAAiB,CAAC,CAAC;QAC3B,CAAC,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC;IACd,CAAC;IAED,IAAI;QACF,IAAA,WAAM,EAAC,IAAI,CAAC,OAAO,EAAE,2BAA2B,CAAC,CAAC;QAClD,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;IACtB,CAAC;IAED,KAAK;QACH,IAAA,WAAM,EAAC,IAAI,CAAC,OAAO,EAAE,2BAA2B,CAAC,CAAC;QAElD,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAC7B,IAAI,CAAC,SAAS,EAAE,KAAK,EAAE,CAAC;QACxB,KAAK,CAAC,kBAAkB,CAAC,CAAC;IAC5B,CAAC;IAED,MAAM;QACJ,IAAA,WAAM,EAAC,IAAI,CAAC,OAAO,EAAE,2BAA2B,CAAC,CAAC;QAElD,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAC7B,IAAI,CAAC,SAAS,EAAE,MAAM,EAAE,CAAC;QACzB,KAAK,CAAC,mBAAmB,CAAC,CAAC;IAC7B,CAAC;IAED,QAAQ;QACN,IAAA,WAAM,EAAC,IAAI,CAAC,OAAO,EAAE,2BAA2B,CAAC,CAAC;QAElD,OAAO,IAAI,CAAC,SAAS,EAAE,QAAQ,EAAE,CAAC;IACpC,CAAC;IAED,MAAM;QACJ,IAAA,WAAM,EAAC,IAAI,EAAE,SAAS,EAAE,2BAA2B,CAAC,CAAC;QACrD,OAAO,iBAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;IACzC,CAAC;CACF;AA3GD,oCA2GC"}