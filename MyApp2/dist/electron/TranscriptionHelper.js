"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TranscriptionHelper = void 0;
const assemblyai_1 = require("assemblyai");
const sox_1 = require("./sox");
const electron_1 = require("electron");
const electron_store_1 = __importDefault(require("electron-store"));
const node_fetch_1 = __importDefault(require("node-fetch"));
const child_process_1 = require("child_process");
class TranscriptionHelper {
    constructor() {
        this.recording = null;
        this.transcript = '';
        this.transcriptChunks = []; // Store transcript chunks in an array for better performance
        this.isTranscribing = false;
        this.SAMPLE_RATE = 16000;
        this.MAX_CHUNKS = 300; // Reduced from 1000 to 300 for better memory management
        this.MAX_TRANSCRIPT_LENGTH = 50000; // Limit total transcript length to prevent memory issues
        this.lastCleanupTime = Date.now();
        // Cache the main window reference for faster updates
        this.mainWindow = null;
        // Get API key from store or environment variables
        const store = new electron_store_1.default();
        const apiKeys = store.get('apiKeys') || { assemblyai: '' };
        const apiKey = apiKeys.assemblyai || process.env.ASSEMBLY_API_KEY || '';
        // Initialize AssemblyAI client
        this.client = new assemblyai_1.AssemblyAI({
            apiKey: apiKey
        });
    }
    /**
     * Check if Sox is installed and accessible
     * @returns True if Sox is installed, false otherwise
     */
    checkSoxInstalled() {
        try {
            // Try to execute sox --version
            (0, child_process_1.execSync)('sox --version', { stdio: 'ignore' });
            return true;
        }
        catch (error) {
            console.error('Sox is not installed or not in PATH:', error);
            return false;
        }
    }
    /**
     * Show a dialog to inform the user about Sox installation
     */
    showSoxInstallationDialog() {
        electron_1.dialog.showMessageBox({
            type: 'warning',
            title: 'Sox Required',
            message: 'The Sox audio tool is required for recording functionality.',
            detail: 'Please install Sox to use the recording features:\n\n' +
                'macOS: brew install sox\n' +
                'Windows: Download from sourceforge.net/projects/sox/\n' +
                'Linux: apt-get install sox or equivalent',
            buttons: ['OK'],
            defaultId: 0
        });
    }
    updateApiKey(apiKey) {
        // Update the client with the new API key
        this.client = new assemblyai_1.AssemblyAI({
            apiKey: apiKey
        });
    }
    async testConnection(apiKey) {
        try {
            // Use the provided API key or the current one
            const authKey = apiKey || this.client.apiKey;
            if (!authKey) {
                return false;
            }
            // Create a temporary client with the provided API key or use the existing one
            const testClient = apiKey
                ? new assemblyai_1.AssemblyAI({ apiKey })
                : this.client;
            // Test the connection using the Lemur task API as suggested in the docs
            try {
                const response = await testClient.lemur.task({
                    prompt: "Verify API connection",
                    final_model: "anthropic/claude-3-5-sonnet",
                    input_text: "This is a test to verify the API connection."
                });
                return !!response;
            }
            catch (lemurError) {
                // If Lemur API fails, try a simpler endpoint
                console.log('Lemur API test failed, trying alternative endpoint:', lemurError);
                const response = await (0, node_fetch_1.default)('https://api.assemblyai.com/v2/transcript', {
                    method: 'GET',
                    headers: {
                        'Authorization': authKey
                    }
                });
                return response.ok;
            }
        }
        catch (error) {
            console.error('AssemblyAI connection test failed:', error);
            return false;
        }
    }
    async startTranscription() {
        if (this.isTranscribing) {
            console.log('Transcription is already running');
            return;
        }
        // Check if Sox is installed
        if (!this.checkSoxInstalled()) {
            console.error('Sox is not installed. Cannot start recording.');
            this.showSoxInstallationDialog();
            return;
        }
        try {
            // Initialize the transcriber
            this.transcriber = this.client.realtime.transcriber({
                sampleRate: this.SAMPLE_RATE
            });
            // Set up event handlers
            this.setupTranscriberEvents();
            // Connect to the real-time transcript service
            console.log('Connecting to real-time transcript service');
            await this.transcriber.connect();
            // Start recording
            console.log('Starting recording');
            try {
                this.recording = new sox_1.SoxRecording({
                    channels: 1,
                    sampleRate: this.SAMPLE_RATE,
                    audioType: 'wav' // Linear PCM
                });
                // Pipe the recording stream to the transcriber stream
                this.recording.stream().pipeTo(this.transcriber.stream());
                this.isTranscribing = true;
            }
            catch (error) {
                console.error('Error starting recording:', error);
                // Show a more user-friendly error message
                electron_1.dialog.showMessageBox({
                    type: 'error',
                    title: 'Recording Error',
                    message: 'Could not start audio recording',
                    detail: 'Please make sure Sox is properly installed and your microphone is working.',
                    buttons: ['OK'],
                    defaultId: 0
                });
                // Clean up if recording failed
                if (this.transcriber) {
                    this.transcriber.close();
                }
            }
        }
        catch (error) {
            console.error('Error starting transcription:', error);
        }
    }
    setupTranscriberEvents() {
        if (!this.transcriber)
            return;
        this.transcriber.on('open', ({ sessionId }) => {
            console.log(`Session opened with ID: ${sessionId}`);
        });
        this.transcriber.on('error', (error) => {
            console.error('Transcription error:', error);
        });
        this.transcriber.on('close', (code, reason) => {
            console.log('Session closed:', code, reason);
            this.isTranscribing = false;
        });
        // Optimize transcript handling to reduce latency and memory usage
        let lastPartialUpdateTime = 0;
        let lastFinalUpdateTime = 0;
        let partialTranscriptBuffer = '';
        const PARTIAL_UPDATE_THROTTLE = 50; // Reduced to 50ms for more responsive partial updates
        const FINAL_UPDATE_THROTTLE = 250; // Reduced to 250ms for more responsive final updates
        this.transcriber.on('transcript', (transcript) => {
            if (!transcript.text) {
                return;
            }
            const now = Date.now();
            // Periodically perform memory management
            this.performMemoryManagement();
            if (transcript.message_type === 'PartialTranscript') {
                // Buffer partial transcripts and update at throttled intervals
                partialTranscriptBuffer = transcript.text;
                // For partial transcripts, use aggressive throttling to reduce rendering overhead
                if (now - lastPartialUpdateTime > PARTIAL_UPDATE_THROTTLE) {
                    this.sendTranscriptUpdate(partialTranscriptBuffer, true);
                    lastPartialUpdateTime = now;
                }
            }
            else {
                // For final transcripts, add to chunks array instead of direct concatenation
                this.transcriptChunks.push(transcript.text + ' ');
                // Limit the number of chunks to prevent memory issues
                if (this.transcriptChunks.length > this.MAX_CHUNKS) {
                    // Join chunks into the base transcript
                    this.transcript += this.transcriptChunks.join('');
                    // If the transcript is too long, trim it
                    if (this.transcript.length > this.MAX_TRANSCRIPT_LENGTH) {
                        this.transcript = this.transcript.slice(-this.MAX_TRANSCRIPT_LENGTH);
                    }
                    // Clear the chunks array
                    this.transcriptChunks = [];
                }
                // Clear the partial transcript buffer
                partialTranscriptBuffer = '';
                // Throttle final transcript updates to reduce UI load
                if (now - lastFinalUpdateTime > FINAL_UPDATE_THROTTLE) {
                    const fullTranscript = this.transcript + this.transcriptChunks.join('');
                    this.sendTranscriptUpdate(fullTranscript, false);
                    lastFinalUpdateTime = now;
                }
            }
        });
    }
    sendTranscriptUpdate(text, isPartial) {
        // Optimize by caching the main window reference
        if (!this.mainWindow || this.mainWindow.isDestroyed()) {
            const windows = electron_1.BrowserWindow.getAllWindows();
            if (windows.length === 0)
                return; // No windows to update
            this.mainWindow = windows[0]; // Cache the main window reference
        }
        // Create the message object once
        const message = { text, isPartial };
        // Send the update directly to the cached window for faster updates
        if (!this.mainWindow.isDestroyed()) {
            // Use the optimized send method
            this.mainWindow.webContents.send('transcript-update', message);
        }
    }
    stopTranscription() {
        if (!this.isTranscribing) {
            console.log('No transcription is running');
            return;
        }
        // Stop the recording
        if (this.recording) {
            this.recording.stop();
            this.recording = null;
        }
        // Close the transcriber connection
        if (this.transcriber) {
            this.transcriber.close();
        }
        this.isTranscribing = false;
    }
    getTranscript() {
        // Efficiently combine the base transcript with chunks
        return this.transcript + this.transcriptChunks.join('');
    }
    // Check if recording is active
    isRecording() {
        return this.isTranscribing;
    }
    clearTranscript() {
        // Clear both the base transcript and chunks array
        this.transcript = '';
        this.transcriptChunks = [];
        this.sendTranscriptUpdate('', false);
    }
    /**
     * Perform memory management to keep the app lightweight
     * This consolidates transcript chunks and trims the transcript if needed
     */
    performMemoryManagement() {
        // Only run cleanup every 30 seconds to avoid excessive processing
        const now = Date.now();
        if (now - this.lastCleanupTime < 30000) {
            return;
        }
        this.lastCleanupTime = now;
        // If we have accumulated chunks, consolidate them
        if (this.transcriptChunks.length > 0) {
            // Join all chunks into the base transcript
            this.transcript += this.transcriptChunks.join('');
            this.transcriptChunks = [];
            // If the transcript is too long, trim it
            if (this.transcript.length > this.MAX_TRANSCRIPT_LENGTH) {
                // Keep only the most recent portion
                this.transcript = this.transcript.slice(-this.MAX_TRANSCRIPT_LENGTH);
            }
        }
        // Force garbage collection if available
        if (global.gc) {
            try {
                global.gc();
            }
            catch (e) {
                console.log('Failed to force garbage collection');
            }
        }
    }
}
exports.TranscriptionHelper = TranscriptionHelper;
//# sourceMappingURL=TranscriptionHelper.js.map