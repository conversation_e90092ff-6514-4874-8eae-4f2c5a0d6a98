"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const electron_1 = require("electron");
const WindowHelper_1 = require("./WindowHelper");
const TranscriptionHelper_1 = require("./TranscriptionHelper");
const OpenAIHelper_1 = require("./OpenAIHelper");
const ShortcutsHelper_1 = require("./ShortcutsHelper");
const dotenv_1 = require("dotenv");
const electron_store_1 = __importDefault(require("electron-store"));
// Load environment variables from .env file
(0, dotenv_1.config)();
// Initialize the store for persistent settings
const store = new electron_store_1.default();
// Set default settings if they don't exist
if (!store.has('settings')) {
    store.set('settings', {
        resume: '',
        jobDescription: '',
        additionalContext: ''
    });
}
if (!store.has('hasDebugged')) {
    store.set('hasDebugged', false);
}
// Set default API keys if they don't exist
if (!store.has('apiKeys')) {
    store.set('apiKeys', {
        openai: '',
        assemblyai: ''
    });
}
class Application {
    constructor() {
        this.currentView = 'transcript';
        this.hasDebugged = store.get('hasDebugged', false);
        this.windowHelper = new WindowHelper_1.WindowHelper(this);
        this.transcriptionHelper = new TranscriptionHelper_1.TranscriptionHelper();
        this.openAIHelper = new OpenAIHelper_1.OpenAIHelper(this);
        this.shortcutsHelper = new ShortcutsHelper_1.ShortcutsHelper(this);
        this.setupAppEvents();
        this.setupIpcHandlers();
    }
    setupAppEvents() {
        // When Electron has finished initialization
        electron_1.app.on('ready', async () => {
            this.windowHelper.createWindow();
            this.shortcutsHelper.registerGlobalShortcuts();
            // Start transcription after a short delay to ensure window is ready
            setTimeout(async () => {
                await this.transcriptionHelper.startTranscription();
            }, 1000);
        });
        // Quit when all windows are closed, except on macOS
        electron_1.app.on('window-all-closed', () => {
            if (process.platform !== 'darwin') {
                electron_1.app.quit();
            }
        });
        electron_1.app.on('activate', () => {
            // On macOS it's common to re-create a window when the dock icon is clicked
            if (electron_1.BrowserWindow.getAllWindows().length === 0) {
                this.windowHelper.createWindow();
            }
        });
        // Clean up before the app quits
        electron_1.app.on('will-quit', () => {
            electron_1.globalShortcut.unregisterAll();
            this.transcriptionHelper.stopTranscription();
        });
    }
    setupIpcHandlers() {
        // Handle IPC messages from the renderer process
        electron_1.ipcMain.handle('get-transcript', () => {
            return this.transcriptionHelper.getTranscript();
        });
        electron_1.ipcMain.handle('clear-transcript', () => {
            this.clearTranscript();
        });
        electron_1.ipcMain.handle('extract-questions', async () => {
            return await this.extractQuestions();
        });
        electron_1.ipcMain.handle('generate-answer', async (_, question) => {
            return await this.generateAnswer(question);
        });
        electron_1.ipcMain.handle('get-user-settings', () => {
            return this.getUserSettings();
        });
        electron_1.ipcMain.handle('save-user-settings', (_, settings) => {
            this.saveUserSettings(settings);
        });
        electron_1.ipcMain.handle('toggle-window', () => {
            this.windowHelper.toggleMainWindow();
        });
        // API key management
        electron_1.ipcMain.handle('get-api-keys', () => {
            return store.get('apiKeys');
        });
        electron_1.ipcMain.handle('save-api-keys', (_event, keys) => {
            try {
                store.set('apiKeys', keys);
                // Update the API keys in the helpers
                this.openAIHelper.updateApiKey(keys.openai);
                this.transcriptionHelper.updateApiKey(keys.assemblyai);
                return { success: true };
            }
            catch (error) {
                console.error('Error saving API keys:', error);
                return { success: false, error: 'Failed to save API keys' };
            }
        });
        electron_1.ipcMain.handle('test-openai-connection', async (_event, apiKey) => {
            try {
                const result = await this.openAIHelper.testConnection(apiKey);
                return { success: result };
            }
            catch (error) {
                console.error('Error testing OpenAI connection:', error);
                return { success: false, error: 'Failed to test OpenAI connection' };
            }
        });
        electron_1.ipcMain.handle('test-assemblyai-connection', async (_event, apiKey) => {
            try {
                const result = await this.transcriptionHelper.testConnection(apiKey);
                return { success: result };
            }
            catch (error) {
                console.error('Error testing AssemblyAI connection:', error);
                return { success: false, error: 'Failed to test AssemblyAI connection' };
            }
        });
        electron_1.ipcMain.handle('clear-api-keys', () => {
            try {
                store.set('apiKeys', { openai: '', assemblyai: '' });
                return { success: true };
            }
            catch (error) {
                console.error('Error clearing API keys:', error);
                return { success: false, error: 'Failed to clear API keys' };
            }
        });
        // Add handler for stopping transcription
        electron_1.ipcMain.handle('stop-transcription', () => {
            this.transcriptionHelper.stopTranscription();
            // Send recording status update to renderer
            const mainWindow = this.windowHelper.getMainWindow();
            if (mainWindow && !mainWindow.isDestroyed()) {
                mainWindow.webContents.send('recording-status-changed', { isRecording: false });
            }
            return { success: true };
        });
        // Add handler for starting transcription
        electron_1.ipcMain.handle('start-transcription', async () => {
            await this.transcriptionHelper.startTranscription();
            // Send recording status update to renderer
            const mainWindow = this.windowHelper.getMainWindow();
            if (mainWindow && !mainWindow.isDestroyed()) {
                mainWindow.webContents.send('recording-status-changed', { isRecording: true });
            }
            return { success: true };
        });
        // Add handler for checking recording status
        electron_1.ipcMain.handle('is-recording', () => {
            return { isRecording: this.transcriptionHelper.isRecording() };
        });
        // Add handler for restarting the application
        electron_1.ipcMain.handle('restart-app', () => {
            this.restartApp();
            return { success: true };
        });
    }
    getMainWindow() {
        return this.windowHelper.getMainWindow();
    }
    getHasDebugged() {
        return this.hasDebugged;
    }
    setHasDebugged(value) {
        this.hasDebugged = value;
        store.set('hasDebugged', value);
    }
    setView(view) {
        this.currentView = view;
        const mainWindow = this.getMainWindow();
        if (mainWindow && !mainWindow.isDestroyed()) {
            mainWindow.webContents.send('set-view', view);
        }
    }
    clearTranscript() {
        this.transcriptionHelper.clearTranscript();
        const mainWindow = this.getMainWindow();
        if (mainWindow && !mainWindow.isDestroyed()) {
            mainWindow.webContents.send('transcript-cleared');
        }
    }
    async extractQuestions() {
        const transcript = this.transcriptionHelper.getTranscript();
        return await this.openAIHelper.extractQuestions(transcript);
    }
    async generateAnswer(question) {
        return await this.openAIHelper.generateAnswer(question);
    }
    getUserSettings() {
        return store.get('settings');
    }
    saveUserSettings(settings) {
        store.set('settings', settings);
    }
    /**
     * Restart the application completely
     * This will quit the app and restart it fresh, clearing all memory
     */
    restartApp() {
        console.log('Restarting application...');
        // Stop transcription to clean up resources
        this.transcriptionHelper.stopTranscription();
        // Force garbage collection if available
        if (global.gc) {
            try {
                global.gc();
                console.log('Forced garbage collection before restart');
            }
            catch (e) {
                console.log('Failed to force garbage collection');
            }
        }
        // Restart the app
        electron_1.app.relaunch();
        electron_1.app.exit(0);
    }
}
// Create and initialize the application
new Application();
//# sourceMappingURL=main.js.map