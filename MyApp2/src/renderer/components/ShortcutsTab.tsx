import React from 'react';

const ShortcutsTab: React.FC = () => {
  // Define all keyboard shortcuts
  const shortcuts = [
    { keys: ['⌘', 'B'], description: 'Toggle Window Visibility' },
    { keys: ['⌘', 'H'], description: 'Extract Questions from Transcript' },
    { keys: ['⌘', 'R'], description: 'Clear Transcript' },
    { keys: ['⌘', 'Enter'], description: 'Generate Answer for Selected Question' },
    { keys: ['⌘', '←'], description: 'Move Window Left' },
    { keys: ['⌘', '→'], description: 'Move Window Right' },
    { keys: ['⌘', '↑'], description: 'Move Window Up' },
    { keys: ['⌘', '↓'], description: 'Move Window Down' },
    { keys: ['⌘', 'Q'], description: 'Restart Application (Clear Memory)' },
    { keys: ['⌘', 'Shift', 'Q'], description: 'Quit Application' }
  ];

  return (
    <div className="shortcuts-tab">
      <p className="shortcuts-description">
        The following keyboard shortcuts are available globally, even when the app is not in focus.
      </p>

      <div className="shortcuts-list">
        {shortcuts.map((shortcut, index) => (
          <div key={index} className="shortcut-item">
            <div className="shortcut-keys">
              {shortcut.keys.map((key, keyIndex) => (
                <React.Fragment key={keyIndex}>
                  <kbd>{key}</kbd>
                  {keyIndex < shortcut.keys.length - 1 && <span>+</span>}
                </React.Fragment>
              ))}
            </div>
            <div className="shortcut-description">{shortcut.description}</div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default ShortcutsTab;
