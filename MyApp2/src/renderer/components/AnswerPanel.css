.answer-panel {
  display: flex;
  flex-direction: column;
  height: 60%;
}

/* Ensure consistent styling in expanded view */
.app-content.answer-expanded .answer-panel {
  height: 100%;
  border-top: none;
}

.generating-indicator {
  font-size: 12px;
  color: var(--success-color);
  display: flex;
  align-items: center;
}

.generating-indicator::before {
  content: '';
  display: inline-block;
  width: 8px;
  height: 8px;
  background-color: var(--success-color);
  border-radius: 50%;
  margin-right: 6px;
  animation: pulse 1.5s infinite;
}

.answer-content {
  flex: 1;
  overflow-y: auto;
  padding: 15px;
  background-color: rgba(0, 0, 0, 0.1);
}

.no-answer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
  color: var(--text-color);
  opacity: 0.7;
  line-height: 1.6;
}

.selected-question {
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.selected-question h3, .answer-text h3 {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 8px;
  color: var(--secondary-color);
}

.selected-question p {
  font-size: 16px;
  line-height: 1.5;
}

.answer-body {
  font-size: 15px;
  line-height: 1.6;
}

/* Markdown styling */
.answer-body p {
  margin-bottom: 16px;
}

.answer-body h1,
.answer-body h2,
.answer-body h3,
.answer-body h4,
.answer-body h5,
.answer-body h6 {
  margin-top: 24px;
  margin-bottom: 16px;
  font-weight: 600;
  line-height: 1.25;
}

.answer-body h1 {
  font-size: 1.5em;
}

.answer-body h2 {
  font-size: 1.3em;
}

.answer-body h3 {
  font-size: 1.1em;
}

.answer-body ul,
.answer-body ol {
  padding-left: 2em;
  margin-bottom: 16px;
}

.answer-body li {
  margin-bottom: 4px;
}

.answer-body a {
  color: var(--highlight-color, #3498db);
  text-decoration: none;
}

.answer-body a:hover {
  text-decoration: underline;
}

.answer-body blockquote {
  padding: 0 1em;
  color: #6a737d;
  border-left: 0.25em solid #dfe2e5;
  margin-bottom: 16px;
}

.answer-body pre {
  margin-bottom: 16px;
  border-radius: 6px;
  overflow: auto;
}

.answer-body code {
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
  padding: 0.2em 0.4em;
  margin: 0;
  font-size: 85%;
  background-color: rgba(27, 31, 35, 0.05);
  border-radius: 3px;
}

.answer-body pre > code {
  padding: 0;
  margin: 0;
  font-size: 100%;
  word-break: normal;
  white-space: pre;
  background: transparent;
  border: 0;
}

.answer-body table {
  border-collapse: collapse;
  width: 100%;
  margin-bottom: 16px;
}

.answer-body table th,
.answer-body table td {
  padding: 6px 13px;
  border: 1px solid #dfe2e5;
}

.answer-body table tr {
  background-color: rgba(0, 0, 0, 0.2);
  border-top: 1px solid #c6cbd1;
}

.answer-body table tr:nth-child(2n) {
  background-color: rgba(0, 0, 0, 0.1);
}

.answer-body img {
  max-width: 100%;
  box-sizing: content-box;
}

.answer-footer {
  display: flex;
  justify-content: flex-end;
  padding: 8px 15px;
  background-color: rgba(0, 0, 0, 0.2);
  border-top: 1px solid var(--border-color);
}
