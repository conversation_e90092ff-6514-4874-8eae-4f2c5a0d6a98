.questions-panel {
  display: flex;
  flex-direction: column;
  height: 40%;
  border-bottom: 1px solid var(--border-color);
}

/* Fix position in expanded view */
.app-content.answer-expanded .questions-panel {
  height: 100%;
  border-bottom: none;
}

.question-count {
  font-size: 12px;
  color: var(--text-color);
  opacity: 0.7;
  background-color: rgba(255, 255, 255, 0.1);
  padding: 2px 6px;
  border-radius: 10px;
}

.questions-list {
  flex: 1;
  overflow-y: auto;
  padding: 10px;
  background-color: rgba(0, 0, 0, 0.1);
}

.no-questions {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
  color: var(--text-color);
  opacity: 0.7;
  line-height: 1.6;
}

.question-item {
  padding: 10px;
  margin-bottom: 8px;
  background-color: rgba(255, 255, 255, 0.05);
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
  display: flex;
}

.question-item:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.question-item.selected {
  background-color: rgba(52, 152, 219, 0.3);
  border-left: 3px solid var(--secondary-color);
}

.question-number {
  margin-right: 8px;
  font-weight: bold;
  color: var(--secondary-color);
}

.question-text {
  flex: 1;
  line-height: 1.4;
  font-size: 14px;
}

.questions-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  background-color: rgba(0, 0, 0, 0.2);
}

.generate-answer-button {
  background-color: var(--secondary-color);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
}

/* Fix button position in expanded view */
.app-content.answer-expanded .generate-answer-button {
  position: relative;
  z-index: 5;
}

.generate-answer-button:hover {
  background-color: #2980b9;
}
