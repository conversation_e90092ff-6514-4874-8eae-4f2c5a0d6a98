.transcript-panel {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background-color: rgba(0, 0, 0, 0.2);
  border-bottom: 1px solid var(--border-color);
}

.panel-header h2 {
  font-size: 16px;
  font-weight: 500;
}

.status-indicator {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: var(--text-color);
  opacity: 0.8;
}

.recording-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 6px;
}

.recording .recording-dot {
  background-color: var(--success-color, #2ecc71);
  animation: pulse 1.5s infinite;
}

.not-recording .recording-dot {
  background-color: var(--highlight-color, #e74c3c);
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.3;
  }
  100% {
    opacity: 1;
  }
}

.transcript-content {
  flex: 1;
  padding: 15px;
  overflow-y: auto;
  background-color: rgba(0, 0, 0, 0.1);
  will-change: transform; /* Hardware acceleration hint */
  transform: translateZ(0); /* Force GPU acceleration */
}

.transcript-text {
  white-space: pre-wrap;
  line-height: 1.5;
  font-size: 14px;
  contain: content; /* Improve rendering performance */
  transform: translateZ(0); /* Force GPU acceleration */
}

.transcript-paragraph {
  margin: 0 0 4px 0;
  padding: 0;
  contain: layout; /* Optimize layout calculations */
  will-change: transform; /* Hint for hardware acceleration */
  transform: translateZ(0); /* Force GPU acceleration */
}

.partial-text {
  color: rgba(255, 255, 255, 0.6);
  font-style: italic;
  display: inline-block; /* Better rendering performance */
  will-change: contents; /* Hint for frequent changes */
  contain: layout; /* Optimize layout calculations */
  transform: translateZ(0); /* Force GPU acceleration */
}

.transcript-footer {
  display: flex;
  justify-content: center;
  padding: 8px 15px;
  background-color: rgba(0, 0, 0, 0.2);
  border-top: 1px solid var(--border-color);
}

.shortcut-hints {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-around;
  width: 100%;
}

.shortcut-hint {
  font-size: 12px;
  color: var(--text-color);
  opacity: 0.7;
  margin: 0 8px;
  white-space: nowrap;
}

kbd {
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
  font-family: monospace;
  font-size: 11px;
  padding: 2px 4px;
  margin: 0 2px;
}
