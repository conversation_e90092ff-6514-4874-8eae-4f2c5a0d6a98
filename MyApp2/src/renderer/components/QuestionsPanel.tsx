import React from 'react';
import './QuestionsPanel.css';

interface QuestionsPanelProps {
  questions: string[];
  selectedQuestion: string;
  onSelectQuestion: (question: string) => void;
  onGenerateAnswer: () => void;
}

const QuestionsPanel: React.FC<QuestionsPanelProps> = ({
  questions,
  selectedQuestion,
  onSelectQuestion,
  onGenerateAnswer
}) => {
  return (
    <div className="questions-panel">
      <div className="panel-header">
        <h2>Extracted Questions</h2>
        {questions.length > 0 && (
          <span className="question-count">{questions.length} found</span>
        )}
      </div>

      <div className="questions-list">
        {questions.length === 0 ? (
          <div className="no-questions">
            <p>No questions extracted yet.</p>
            <p>Press <kbd>⌘</kbd> + <kbd>H</kbd> to extract questions from the transcript.</p>
          </div>
        ) : (
          questions.map((question, index) => (
            <div
              key={index}
              className={`question-item ${selectedQuestion === question ? 'selected' : ''}`}
              onClick={() => onSelectQuestion(question)}
            >
              <span className="question-number">{index + 1}.</span>
              <span className="question-text">{question}</span>
            </div>
          ))
        )}
      </div>

      {selectedQuestion && (
        <div className="questions-footer">
          <button
            className="generate-answer-button"
            onClick={onGenerateAnswer}
            title="Generate Answer (Cmd+Enter)"
          >
            Generate Answer
          </button>

        </div>
      )}
    </div>
  );
};

export default QuestionsPanel;
