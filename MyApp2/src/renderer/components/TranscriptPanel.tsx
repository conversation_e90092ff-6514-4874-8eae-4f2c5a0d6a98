import React, { useRef, useEffect, memo, useMemo, useState, useCallback } from 'react';
import './TranscriptPanel.css';

// Debounce function to limit how often a function can be called
function debounce<T extends (...args: any[]) => any>(func: T, wait: number): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null;

  return function(...args: Parameters<T>): void {
    if (timeout) {
      clearTimeout(timeout);
    }

    timeout = setTimeout(() => {
      func(...args);
      timeout = null;
    }, wait);
  };
}

interface TranscriptPanelProps {
  transcript: string;
  partialTranscript: string;
  isRecording?: boolean;
}

// Use memo to prevent unnecessary re-renders
const TranscriptPanel: React.FC<TranscriptPanelProps> = memo(({ transcript, partialTranscript, isRecording = true }) => {
  const transcriptRef = useRef<HTMLDivElement>(null);
  const prevHeightRef = useRef<number>(0);
  const lastTranscriptRef = useRef<string>(transcript);
  const lastPartialRef = useRef<string>(partialTranscript);

  // Use state for rendering optimization
  const [shouldUpdate, setShouldUpdate] = useState<boolean>(true);

  // Track if user has manually scrolled up
  const [userScrolledUp, setUserScrolledUp] = useState<boolean>(false);

  // Debounced update function with reduced delay for faster updates
  const debouncedSetShouldUpdate = useCallback(
    debounce((value: boolean) => {
      setShouldUpdate(value);
    }, 16), // Reduced to 16ms (60fps) for more responsive updates
    []
  );

  // Check if we need to update the component - with optimized update detection
  useEffect(() => {
    // For partial transcript, we want to be more responsive
    if (partialTranscript !== lastPartialRef.current) {
      lastPartialRef.current = partialTranscript;
      // Update immediately for partial transcript changes to minimize delay
      setShouldUpdate(true);
      return;
    }

    // For full transcript, check if there's a meaningful change
    if (transcript !== lastTranscriptRef.current) {
      const transcriptLengthDiff = Math.abs(transcript.length - lastTranscriptRef.current.length);

      // More responsive update threshold (reduced from 5 to 2 characters)
      if (transcriptLengthDiff > 2) {
        lastTranscriptRef.current = transcript;
        debouncedSetShouldUpdate(true);
      }
    }
  }, [transcript, partialTranscript, debouncedSetShouldUpdate]);

  // Handle scroll events to detect when user scrolls up
  const handleScroll = useCallback(() => {
    if (transcriptRef.current) {
      const element = transcriptRef.current;
      const isAtBottom = element.scrollHeight - element.clientHeight <= element.scrollTop + 50;
      setUserScrolledUp(!isAtBottom);
    }
  }, []);

  // Add scroll event listener
  useEffect(() => {
    const element = transcriptRef.current;
    if (element) {
      element.addEventListener('scroll', handleScroll);
      return () => {
        element.removeEventListener('scroll', handleScroll);
      };
    }
  }, [handleScroll]);

  // Optimized auto-scroll to the bottom when transcript updates
  useEffect(() => {
    if (!shouldUpdate) return;

    if (transcriptRef.current) {
      const element = transcriptRef.current;

      // Only auto-scroll if user hasn't manually scrolled up
      if (!userScrolledUp) {
        // Use immediate scrolling for partial transcript updates to minimize delay
        if (partialTranscript) {
          element.scrollTop = element.scrollHeight;
          prevHeightRef.current = element.scrollHeight;
          setShouldUpdate(false);
        } else {
          // Use requestAnimationFrame for smoother scrolling on full transcript updates
          requestAnimationFrame(() => {
            element.scrollTop = element.scrollHeight;
            prevHeightRef.current = element.scrollHeight;
            setShouldUpdate(false);
          });
        }
      } else {
        setShouldUpdate(false);
      }
    }
  }, [shouldUpdate, userScrolledUp, partialTranscript]);

  // Keyboard shortcuts for window movement
  const movementShortcuts = [
    { keys: '⌘ + ←', action: 'Move Left' },
    { keys: '⌘ + →', action: 'Move Right' },
    { keys: '⌘ + ↑', action: 'Move Up' },
    { keys: '⌘ + ↓', action: 'Move Down' }
  ];

  return (
    <div className="transcript-panel">
      <div className="panel-header">
        <h2>Transcript</h2>
        <div className={`status-indicator ${isRecording ? 'recording' : 'not-recording'}`}>
          <span className="recording-dot"></span>
          {isRecording ? 'Recording' : 'Not Recording'}
        </div>
      </div>
      <div className="transcript-content" ref={transcriptRef}>
        <div className="transcript-text">
          {useMemo(() => {
            // Memoize the transcript content to prevent unnecessary re-renders
            // Only show the last 15,000 characters to balance history and performance
            const displayTranscript = transcript.length > 15000
              ? transcript.slice(-15000)
              : transcript;

            // Pre-process the transcript for faster rendering
            // Split into paragraphs for better performance
            const paragraphs = displayTranscript.split('\n').filter(p => p.trim() !== '');

            return (
              <>
                {paragraphs.length > 0 ? (
                  paragraphs.map((paragraph, index) => (
                    <p key={index} className="transcript-paragraph">{paragraph}</p>
                  ))
                ) : (
                  displayTranscript
                )}
                {partialTranscript && (
                  <span className="partial-text">{partialTranscript}</span>
                )}
              </>
            );
          }, [transcript, partialTranscript])}
        </div>
      </div>

    </div>
  );
});

export default TranscriptPanel;
