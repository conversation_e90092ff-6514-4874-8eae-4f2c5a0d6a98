interface ElectronAPI {
  // Transcript functions
  getTranscript: () => Promise<string>;
  clearTranscript: () => Promise<void>;
  startTranscription: () => Promise<{ success: boolean }>;
  stopTranscription: () => Promise<{ success: boolean }>;
  isRecording: () => Promise<{ isRecording: boolean }>;
  onTranscriptUpdate: (callback: (data: { text: string, isPartial: boolean }) => void) => () => void;
  onTranscriptCleared: (callback: () => void) => () => void;

  // Question extraction and answer generation
  extractQuestions: () => Promise<string[]>;
  onQuestionsExtracted: (callback: (questions: string[]) => void) => () => void;
  generateAnswer: (question: string) => Promise<string>;
  onGenerateAnswerRequest: (callback: () => void) => () => void;

  // User settings
  getUserSettings: () => Promise<{
    resume: string;
    jobDescription: string;
    additionalContext: string;
  }>;
  saveUserSettings: (settings: {
    resume: string;
    jobDescription: string;
    additionalContext: string;
  }) => Promise<void>;

  // API key management
  getApiKeys: () => Promise<{ openai: string, assemblyai: string }>;
  saveApiKeys: (keys: { openai: string, assemblyai: string }) => Promise<{ success: boolean, error?: string }>;
  testOpenAIConnection: (apiKey?: string) => Promise<{ success: boolean, error?: string }>;
  testAssemblyAIConnection: (apiKey?: string) => Promise<{ success: boolean, error?: string }>;
  clearApiKeys: () => Promise<{ success: boolean, error?: string }>;

  // Window management
  toggleWindow: () => Promise<void>;
  onToggleWindow: (callback: () => void) => () => void;
  onSetView: (callback: (view: string) => void) => () => void;

  // Recording status
  onRecordingStatusChanged: (callback: (data: { isRecording: boolean }) => void) => () => void;
  
  // App restart functionality
  restartApp: () => Promise<{ success: boolean }>;
}

interface Window {
  electronAPI: ElectronAPI;
}
