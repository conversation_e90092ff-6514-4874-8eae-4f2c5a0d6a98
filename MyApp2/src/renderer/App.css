:root {
  --primary-color: #2c3e50;
  --secondary-color: #3498db;
  --background-color: rgba(44, 62, 80, 0.9);
  --text-color: #ecf0f1;
  --border-color: #7f8c8d;
  --highlight-color: #e74c3c;
  --success-color: #2ecc71;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
  color: var(--text-color);
  background-color: transparent;
  overflow: hidden;
}

.app {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: var(--background-color);
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.app-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 20px;
  background-color: var(--primary-color);
  border-bottom: 1px solid var(--border-color);
  -webkit-app-region: drag;
}

.app-header h1 {
  font-size: 18px;
  font-weight: 500;
}

.header-buttons {
  display: flex;
  gap: 10px;
  -webkit-app-region: no-drag;
}

.recording-controls {
  margin-right: 5px;
}

.start-recording-button {
  background-color: #2ecc71;
  color: white;
}

.start-recording-button:hover {
  background-color: #27ae60;
}

.stop-recording-button {
  background-color: #e74c3c;
  color: white;
}

.stop-recording-button:hover {
  background-color: #c0392b;
}

.header-buttons button {
  background-color: var(--secondary-color);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 12px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.header-buttons button:hover {
  background-color: #2980b9;
}

.settings-button, .expand-button, .restart-button {
  background-color: transparent !important;
  font-size: 14px !important;
  padding: 6px !important;
}

.expand-button {
  background-color: rgba(52, 152, 219, 0.2) !important;
}

.restart-button {
  font-size: 16px !important;
  padding: 4px 8px !important;
  margin-left: 5px;
  color: #e74c3c !important;
  transition: transform 0.2s, color 0.2s !important;
}

.restart-button:hover {
  transform: rotate(180deg);
  color: #c0392b !important;
}

.app-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

/* Expanded answer panel layout */
.app-content.answer-expanded {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 35% 65%;
  grid-template-areas:
    "transcript questions"
    "answer answer";
  gap: 0;
}

.left-panel {
  flex: 1;
  border-right: 1px solid var(--border-color);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.right-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* Grid layout for expanded view */
.answer-expanded .left-panel {
  grid-area: transcript;
  border-right: 1px solid var(--border-color);
  border-bottom: 1px solid var(--border-color);
  max-height: 100%;
  overflow: auto;
}

.answer-expanded .right-panel {
  display: flex;
  flex-direction: column;
}

.answer-expanded .right-panel > div:first-child {
  grid-area: questions;
  border-bottom: 1px solid var(--border-color);
  height: 100%;
  overflow: auto;
}

.answer-expanded .right-panel > div:last-child {
  grid-area: answer;
  grid-column: 1 / span 2;
  overflow: auto;
}

/* Position the answer panel in expanded view */
.answer-expanded .right-panel > div:first-child {
  position: relative;
  z-index: 2;
}

.answer-expanded .right-panel > div:last-child {
  position: absolute;
  top: calc(35% + 41px); /* Header height + top panel height */
  left: 0;
  right: 0;
  bottom: 28px; /* Footer height */
  height: auto;
  border-top: none;
  margin-top: 0;
}

/* Remove gap between panels in expanded view */
.answer-expanded .right-panel > div:last-child .panel-header {
  border-top: 1px solid var(--border-color);
  margin-top: 0;
  padding-top: 10px;
  padding-bottom: 10px;
  background-color: var(--panel-header-bg);
}

/* Responsive layout */
@media (max-width: 768px) {
  .app-content {
    flex-direction: column;
  }

  .left-panel {
    border-right: none;
    border-bottom: 1px solid var(--border-color);
  }
}
