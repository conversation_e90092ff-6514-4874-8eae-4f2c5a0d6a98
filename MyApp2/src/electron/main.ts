import { app, BrowserWindow, ipcMain, globalShortcut } from 'electron';
import path from 'path';
import { WindowHelper } from './WindowHelper';
import { TranscriptionHelper } from './TranscriptionHelper';
import { OpenAIHelper } from './OpenAIHelper';
import { ShortcutsHelper } from './ShortcutsHelper';
import { config } from 'dotenv';
import Store from 'electron-store';

// Load environment variables from .env file
config();

// Define the AppState interface
export interface AppState {
  getMainWindow(): BrowserWindow | null;
  getHasDebugged(): boolean;
  setView(view: string): void;
  transcriptionHelper: TranscriptionHelper;
  openAIHelper: OpenAIHelper;
  windowHelper: WindowHelper;
  clearTranscript(): void;
  extractQuestions(): Promise<string[]>;
  generateAnswer(question: string): Promise<string>;
  getUserSettings(): UserSettings;
  saveUserSettings(settings: UserSettings): void;
  restartApp(): void;
}

export interface UserSettings {
  resume: string;
  jobDescription: string;
  additionalContext: string;
}

// Initialize the store for persistent settings
const store = new Store<{
  settings: UserSettings;
  hasDebugged: boolean;
  apiKeys: {
    openai: string;
    assemblyai: string;
  };
}>();

// Set default settings if they don't exist
if (!store.has('settings')) {
  store.set('settings', {
    resume: '',
    jobDescription: '',
    additionalContext: ''
  });
}

if (!store.has('hasDebugged')) {
  store.set('hasDebugged', false);
}

// Set default API keys if they don't exist
if (!store.has('apiKeys')) {
  store.set('apiKeys', {
    openai: '',
    assemblyai: ''
  });
}

class Application implements AppState {
  public windowHelper: WindowHelper;
  private shortcutsHelper: ShortcutsHelper;
  public transcriptionHelper: TranscriptionHelper;
  public openAIHelper: OpenAIHelper;
  private currentView: string = 'transcript';
  private hasDebugged: boolean = store.get('hasDebugged', false);

  constructor() {
    this.windowHelper = new WindowHelper(this);
    this.transcriptionHelper = new TranscriptionHelper();
    this.openAIHelper = new OpenAIHelper(this);
    this.shortcutsHelper = new ShortcutsHelper(this);

    this.setupAppEvents();
    this.setupIpcHandlers();
  }

  private setupAppEvents(): void {
    // When Electron has finished initialization
    app.on('ready', async () => {
      this.windowHelper.createWindow();
      this.shortcutsHelper.registerGlobalShortcuts();

      // Start transcription after a short delay to ensure window is ready
      setTimeout(async () => {
        await this.transcriptionHelper.startTranscription();
      }, 1000);
    });

    // Quit when all windows are closed, except on macOS
    app.on('window-all-closed', () => {
      if (process.platform !== 'darwin') {
        app.quit();
      }
    });

    app.on('activate', () => {
      // On macOS it's common to re-create a window when the dock icon is clicked
      if (BrowserWindow.getAllWindows().length === 0) {
        this.windowHelper.createWindow();
      }
    });

    // Clean up before the app quits
    app.on('will-quit', () => {
      globalShortcut.unregisterAll();
      this.transcriptionHelper.stopTranscription();
    });
  }

  private setupIpcHandlers(): void {
    // Handle IPC messages from the renderer process
    ipcMain.handle('get-transcript', () => {
      return this.transcriptionHelper.getTranscript();
    });

    ipcMain.handle('clear-transcript', () => {
      this.clearTranscript();
    });

    ipcMain.handle('extract-questions', async () => {
      return await this.extractQuestions();
    });

    ipcMain.handle('generate-answer', async (_, question: string) => {
      return await this.generateAnswer(question);
    });

    ipcMain.handle('get-user-settings', () => {
      return this.getUserSettings();
    });

    ipcMain.handle('save-user-settings', (_, settings: UserSettings) => {
      this.saveUserSettings(settings);
    });

    ipcMain.handle('toggle-window', () => {
      this.windowHelper.toggleMainWindow();
    });

    // API key management
    ipcMain.handle('get-api-keys', () => {
      return store.get('apiKeys');
    });

    ipcMain.handle('save-api-keys', (_event, keys: { openai: string, assemblyai: string }) => {
      try {
        store.set('apiKeys', keys);

        // Update the API keys in the helpers
        this.openAIHelper.updateApiKey(keys.openai);
        this.transcriptionHelper.updateApiKey(keys.assemblyai);

        return { success: true };
      } catch (error) {
        console.error('Error saving API keys:', error);
        return { success: false, error: 'Failed to save API keys' };
      }
    });

    ipcMain.handle('test-openai-connection', async (_event, apiKey?: string) => {
      try {
        const result = await this.openAIHelper.testConnection(apiKey);
        return { success: result };
      } catch (error) {
        console.error('Error testing OpenAI connection:', error);
        return { success: false, error: 'Failed to test OpenAI connection' };
      }
    });

    ipcMain.handle('test-assemblyai-connection', async (_event, apiKey?: string) => {
      try {
        const result = await this.transcriptionHelper.testConnection(apiKey);
        return { success: result };
      } catch (error) {
        console.error('Error testing AssemblyAI connection:', error);
        return { success: false, error: 'Failed to test AssemblyAI connection' };
      }
    });

    ipcMain.handle('clear-api-keys', () => {
      try {
        store.set('apiKeys', { openai: '', assemblyai: '' });
        return { success: true };
      } catch (error) {
        console.error('Error clearing API keys:', error);
        return { success: false, error: 'Failed to clear API keys' };
      }
    });

    // Add handler for stopping transcription
    ipcMain.handle('stop-transcription', () => {
      this.transcriptionHelper.stopTranscription();

      // Send recording status update to renderer
      const mainWindow = this.windowHelper.getMainWindow();
      if (mainWindow && !mainWindow.isDestroyed()) {
        mainWindow.webContents.send('recording-status-changed', { isRecording: false });
      }

      return { success: true };
    });

    // Add handler for starting transcription
    ipcMain.handle('start-transcription', async () => {
      await this.transcriptionHelper.startTranscription();

      // Send recording status update to renderer
      const mainWindow = this.windowHelper.getMainWindow();
      if (mainWindow && !mainWindow.isDestroyed()) {
        mainWindow.webContents.send('recording-status-changed', { isRecording: true });
      }

      return { success: true };
    });

    // Add handler for checking recording status
    ipcMain.handle('is-recording', () => {
      return { isRecording: this.transcriptionHelper.isRecording() };
    });

    // Add handler for restarting the application
    ipcMain.handle('restart-app', () => {
      this.restartApp();
      return { success: true };
    });
  }

  public getMainWindow(): BrowserWindow | null {
    return this.windowHelper.getMainWindow();
  }

  public getHasDebugged(): boolean {
    return this.hasDebugged;
  }

  public setHasDebugged(value: boolean): void {
    this.hasDebugged = value;
    store.set('hasDebugged', value);
  }

  public setView(view: string): void {
    this.currentView = view;
    const mainWindow = this.getMainWindow();
    if (mainWindow && !mainWindow.isDestroyed()) {
      mainWindow.webContents.send('set-view', view);
    }
  }

  public clearTranscript(): void {
    this.transcriptionHelper.clearTranscript();
    const mainWindow = this.getMainWindow();
    if (mainWindow && !mainWindow.isDestroyed()) {
      mainWindow.webContents.send('transcript-cleared');
    }
  }

  public async extractQuestions(): Promise<string[]> {
    const transcript = this.transcriptionHelper.getTranscript();
    return await this.openAIHelper.extractQuestions(transcript);
  }

  public async generateAnswer(question: string): Promise<string> {
    return await this.openAIHelper.generateAnswer(question);
  }

  public getUserSettings(): UserSettings {
    return store.get('settings') as UserSettings;
  }

  public saveUserSettings(settings: UserSettings): void {
    store.set('settings', settings);
  }

  /**
   * Restart the application completely
   * This will quit the app and restart it fresh, clearing all memory
   */
  public restartApp(): void {
    console.log('Restarting application...');

    // Stop transcription to clean up resources
    this.transcriptionHelper.stopTranscription();

    // Force garbage collection if available
    if (global.gc) {
      try {
        global.gc();
        console.log('Forced garbage collection before restart');
      } catch (e) {
        console.log('Failed to force garbage collection');
      }
    }

    // Restart the app
    app.relaunch();
    app.exit(0);
  }
}

// Add global.gc declaration for TypeScript
declare global {
  namespace NodeJS {
    interface Global {
      gc?: () => void;
    }
  }
}

// Create and initialize the application
new Application();
