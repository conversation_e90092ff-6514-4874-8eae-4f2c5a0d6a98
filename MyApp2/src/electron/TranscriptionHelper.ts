import { <PERSON><PERSON><PERSON> } from 'assemblyai';
import { SoxRecording } from './sox';
import { BrowserWindow, dialog } from 'electron';
import Store from 'electron-store';
import fetch from 'node-fetch';
import { existsSync } from 'fs';
import { execSync } from 'child_process';

// Add global.gc declaration for TypeScript
declare global {
  namespace NodeJS {
    interface Global {
      gc?: () => void;
    }
  }
}

interface RealtimeTranscript {
  text?: string;
  message_type?: string;
}

export class TranscriptionHelper {
  private client: AssemblyAI;
  private transcriber: any; // Using any for now due to type complexity
  private recording: SoxRecording | null = null;
  private transcript: string = '';
  private transcriptChunks: string[] = []; // Store transcript chunks in an array for better performance
  private isTranscribing: boolean = false;
  private SAMPLE_RATE = 16000;
  private MAX_CHUNKS = 300; // Reduced from 1000 to 300 for better memory management
  private MAX_TRANSCRIPT_LENGTH = 50000; // Limit total transcript length to prevent memory issues
  private lastCleanupTime = Date.now();

  constructor() {
    // Get API key from store or environment variables
    const store = new Store();
    const apiKeys = store.get('apiKeys') as { assemblyai: string } || { assemblyai: '' };
    const apiKey = apiKeys.assemblyai || process.env.ASSEMBLY_API_KEY || '';

    // Initialize AssemblyAI client
    this.client = new AssemblyAI({
      apiKey: apiKey
    });
  }

  /**
   * Check if Sox is installed and accessible
   * @returns True if Sox is installed, false otherwise
   */
  private checkSoxInstalled(): boolean {
    try {
      // Try to execute sox --version
      execSync('sox --version', { stdio: 'ignore' });
      return true;
    } catch (error) {
      console.error('Sox is not installed or not in PATH:', error);
      return false;
    }
  }

  /**
   * Show a dialog to inform the user about Sox installation
   */
  private showSoxInstallationDialog(): void {
    dialog.showMessageBox({
      type: 'warning',
      title: 'Sox Required',
      message: 'The Sox audio tool is required for recording functionality.',
      detail: 'Please install Sox to use the recording features:\n\n' +
              'macOS: brew install sox\n' +
              'Windows: Download from sourceforge.net/projects/sox/\n' +
              'Linux: apt-get install sox or equivalent',
      buttons: ['OK'],
      defaultId: 0
    });
  }

  public updateApiKey(apiKey: string): void {
    // Update the client with the new API key
    this.client = new AssemblyAI({
      apiKey: apiKey
    });
  }

  public async testConnection(apiKey?: string): Promise<boolean> {
    try {
      // Use the provided API key or the current one
      const authKey = apiKey || (this.client as any).apiKey;

      if (!authKey) {
        return false;
      }

      // Create a temporary client with the provided API key or use the existing one
      const testClient = apiKey
        ? new AssemblyAI({ apiKey })
        : this.client;

      // Test the connection using the Lemur task API as suggested in the docs
      try {
        const response = await testClient.lemur.task({
          prompt: "Verify API connection",
          final_model: "anthropic/claude-3-5-sonnet",
          input_text: "This is a test to verify the API connection."
        });

        return !!response;
      } catch (lemurError) {
        // If Lemur API fails, try a simpler endpoint
        console.log('Lemur API test failed, trying alternative endpoint:', lemurError);

        const response = await fetch('https://api.assemblyai.com/v2/transcript', {
          method: 'GET',
          headers: {
            'Authorization': authKey
          }
        });

        return response.ok;
      }
    } catch (error) {
      console.error('AssemblyAI connection test failed:', error);
      return false;
    }
  }



  public async startTranscription(): Promise<void> {
    if (this.isTranscribing) {
      console.log('Transcription is already running');
      return;
    }

    // Check if Sox is installed
    if (!this.checkSoxInstalled()) {
      console.error('Sox is not installed. Cannot start recording.');
      this.showSoxInstallationDialog();
      return;
    }

    try {
      // Initialize the transcriber
      this.transcriber = this.client.realtime.transcriber({
        sampleRate: this.SAMPLE_RATE
      });

      // Set up event handlers
      this.setupTranscriberEvents();

      // Connect to the real-time transcript service
      console.log('Connecting to real-time transcript service');
      await this.transcriber.connect();

      // Start recording
      console.log('Starting recording');
      try {
        this.recording = new SoxRecording({
          channels: 1,
          sampleRate: this.SAMPLE_RATE,
          audioType: 'wav' // Linear PCM
        });

        // Pipe the recording stream to the transcriber stream
        this.recording.stream().pipeTo(this.transcriber.stream());
        this.isTranscribing = true;
      } catch (error) {
        console.error('Error starting recording:', error);

        // Show a more user-friendly error message
        dialog.showMessageBox({
          type: 'error',
          title: 'Recording Error',
          message: 'Could not start audio recording',
          detail: 'Please make sure Sox is properly installed and your microphone is working.',
          buttons: ['OK'],
          defaultId: 0
        });

        // Clean up if recording failed
        if (this.transcriber) {
          this.transcriber.close();
        }
      }
    } catch (error) {
      console.error('Error starting transcription:', error);
    }
  }

  private setupTranscriberEvents(): void {
    if (!this.transcriber) return;

    this.transcriber.on('open', ({ sessionId }: { sessionId: string }) => {
      console.log(`Session opened with ID: ${sessionId}`);
    });

    this.transcriber.on('error', (error: Error) => {
      console.error('Transcription error:', error);
    });

    this.transcriber.on('close', (code: number, reason: string) => {
      console.log('Session closed:', code, reason);
      this.isTranscribing = false;
    });

    // Optimize transcript handling to reduce latency and memory usage
    let lastPartialUpdateTime = 0;
    let lastFinalUpdateTime = 0;
    let partialTranscriptBuffer = '';
    const PARTIAL_UPDATE_THROTTLE = 50; // Reduced to 50ms for more responsive partial updates
    const FINAL_UPDATE_THROTTLE = 250; // Reduced to 250ms for more responsive final updates

    this.transcriber.on('transcript', (transcript: RealtimeTranscript) => {
      if (!transcript.text) {
        return;
      }

      const now = Date.now();

      // Periodically perform memory management
      this.performMemoryManagement();

      if (transcript.message_type === 'PartialTranscript') {
        // Buffer partial transcripts and update at throttled intervals
        partialTranscriptBuffer = transcript.text;

        // For partial transcripts, use aggressive throttling to reduce rendering overhead
        if (now - lastPartialUpdateTime > PARTIAL_UPDATE_THROTTLE) {
          this.sendTranscriptUpdate(partialTranscriptBuffer, true);
          lastPartialUpdateTime = now;
        }
      } else {
        // For final transcripts, add to chunks array instead of direct concatenation
        this.transcriptChunks.push(transcript.text + ' ');

        // Limit the number of chunks to prevent memory issues
        if (this.transcriptChunks.length > this.MAX_CHUNKS) {
          // Join chunks into the base transcript
          this.transcript += this.transcriptChunks.join('');

          // If the transcript is too long, trim it
          if (this.transcript.length > this.MAX_TRANSCRIPT_LENGTH) {
            this.transcript = this.transcript.slice(-this.MAX_TRANSCRIPT_LENGTH);
          }

          // Clear the chunks array
          this.transcriptChunks = [];
        }

        // Clear the partial transcript buffer
        partialTranscriptBuffer = '';

        // Throttle final transcript updates to reduce UI load
        if (now - lastFinalUpdateTime > FINAL_UPDATE_THROTTLE) {
          const fullTranscript = this.transcript + this.transcriptChunks.join('');
          this.sendTranscriptUpdate(fullTranscript, false);
          lastFinalUpdateTime = now;
        }
      }
    });
  }

  // Cache the main window reference for faster updates
  private mainWindow: BrowserWindow | null = null;

  private sendTranscriptUpdate(text: string, isPartial: boolean): void {
    // Optimize by caching the main window reference
    if (!this.mainWindow || this.mainWindow.isDestroyed()) {
      const windows = BrowserWindow.getAllWindows();
      if (windows.length === 0) return; // No windows to update
      this.mainWindow = windows[0]; // Cache the main window reference
    }

    // Create the message object once
    const message = { text, isPartial };

    // Send the update directly to the cached window for faster updates
    if (!this.mainWindow.isDestroyed()) {
      // Use the optimized send method
      this.mainWindow.webContents.send('transcript-update', message);
    }
  }

  public stopTranscription(): void {
    if (!this.isTranscribing) {
      console.log('No transcription is running');
      return;
    }

    // Stop the recording
    if (this.recording) {
      this.recording.stop();
      this.recording = null;
    }

    // Close the transcriber connection
    if (this.transcriber) {
      this.transcriber.close();
    }

    this.isTranscribing = false;
  }

  public getTranscript(): string {
    // Efficiently combine the base transcript with chunks
    return this.transcript + this.transcriptChunks.join('');
  }

  // Check if recording is active
  public isRecording(): boolean {
    return this.isTranscribing;
  }

  public clearTranscript(): void {
    // Clear both the base transcript and chunks array
    this.transcript = '';
    this.transcriptChunks = [];
    this.sendTranscriptUpdate('', false);
  }

  /**
   * Perform memory management to keep the app lightweight
   * This consolidates transcript chunks and trims the transcript if needed
   */
  public performMemoryManagement(): void {
    // Only run cleanup every 30 seconds to avoid excessive processing
    const now = Date.now();
    if (now - this.lastCleanupTime < 30000) {
      return;
    }

    this.lastCleanupTime = now;

    // If we have accumulated chunks, consolidate them
    if (this.transcriptChunks.length > 0) {
      // Join all chunks into the base transcript
      this.transcript += this.transcriptChunks.join('');
      this.transcriptChunks = [];

      // If the transcript is too long, trim it
      if (this.transcript.length > this.MAX_TRANSCRIPT_LENGTH) {
        // Keep only the most recent portion
        this.transcript = this.transcript.slice(-this.MAX_TRANSCRIPT_LENGTH);
      }
    }

    // Force garbage collection if available
    if (global.gc) {
      try {
        global.gc();
      } catch (e) {
        console.log('Failed to force garbage collection');
      }
    }
  }
}
