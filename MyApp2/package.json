{"name": "interview-assistant", "version": "1.0.0", "description": "An interview assistant application with real-time transcription and AI-powered answers", "main": "dist/electron/main.js", "scripts": {"start": "electron .", "dev": "concurrently \"npm run dev:vite\" \"npm run dev:electron\"", "dev:vite": "vite", "dev:electron": "tsc -p tsconfig.electron.json && electron .", "build": "npm run build:electron && npm run build:renderer", "build:electron": "tsc -p tsconfig.electron.json", "build:renderer": "vite build", "package": "electron-builder"}, "keywords": ["interview", "assistant", "transcription", "electron", "openai"], "author": "", "license": "MIT", "dependencies": {"assemblyai": "^4.9.0", "dotenv": "^16.3.1", "electron-store": "^8.1.0", "node-fetch": "^2.7.0", "openai": "^4.20.1", "react-markdown": "^10.1.0", "react-syntax-highlighter": "^15.6.1"}, "devDependencies": {"@types/node": "^20.17.30", "@types/react": "^18.2.38", "@types/react-dom": "^18.2.17", "@vitejs/plugin-react": "^4.2.0", "concurrently": "^8.2.2", "electron": "^27.1.2", "electron-builder": "^24.6.4", "react": "^18.2.0", "react-dom": "^18.2.0", "rimraf": "^6.0.1", "typescript": "^5.3.2", "vite": "^5.0.2"}, "build": {"appId": "com.interview.assistant", "productName": "Interview Assistant", "mac": {"category": "public.app-category.productivity", "icon": "assets/icon.icns", "target": ["dmg", "zip"], "darkModeSupport": true}, "dmg": {"icon": "assets/icon.icns", "title": "Install Interview Assistant", "contents": [{"x": 130, "y": 220}, {"x": 410, "y": 220, "type": "link", "path": "/Applications"}], "window": {"width": 540, "height": 380}}, "files": ["dist/**/*", "node_modules/**/*"], "directories": {"buildResources": "assets", "output": "release"}}}