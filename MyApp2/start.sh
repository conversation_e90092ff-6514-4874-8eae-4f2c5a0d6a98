#!/bin/bash

# Navigate to the application directory
cd "$(dirname "$0")"

# Check if node_modules exists, if not install dependencies
if [ ! -d "node_modules" ]; then
  echo "Installing dependencies..."
  npm install
fi

# Check if the application is built, if not build it
if [ ! -d "dist/electron" ]; then
  echo "Building application..."
  npm run build
fi

# Start the application
echo "Starting Interview Assistant..."
npx electron .
