#!/bin/bash

# Display what's happening
echo "Starting Interview Coder application..."

# Ensure we're in the right directory
cd "$(dirname "$0")"

# Kill any existing processes that might interfere
echo "Cleaning up any existing processes..."
pkill -f "node.*vite" 2>/dev/null || true
pkill -f "electron" 2>/dev/null || true
sleep 1

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    echo "Error: npm is not installed or not in PATH"
    exit 1
fi

# Check if dependencies are installed
if [ ! -d "node_modules" ] || [ ! -d "node_modules/electron" ]; then
    echo "Dependencies not found. Please run 'bash build.sh' first."
    exit 1
fi

# Start the application directly in the current terminal
echo "Starting the application in development mode..."
npm run app:dev